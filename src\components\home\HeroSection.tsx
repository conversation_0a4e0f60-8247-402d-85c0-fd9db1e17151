import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Phone, CheckCircle, Car, Wrench, MapPin, Clock, Shield, Star } from "lucide-react";

const HeroSection = () => {
  return (
    <section className="relative w-full bg-gradient-to-br from-automotive-dark to-automotive-blue text-white overflow-hidden py-12 md:py-16">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-automotive-orange/20 rounded-full mix-blend-overlay filter blur-3xl opacity-30 animate-float-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-automotive-blue/30 rounded-full mix-blend-overlay filter blur-3xl opacity-30 animate-float-slow animation-delay-2000"></div>
        <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-5"></div>
      </div>

      <div className="relative container mx-auto px-4 z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12">
          {/* Left Content Area */}
          <div className="lg:w-1/2 text-center lg:text-left space-y-5 animate-fade-in-up">
            <div className="inline-flex items-center px-3 py-1 bg-white/10 backdrop-blur-sm rounded-full">
              <Star className="w-4 h-4 text-automotive-orange mr-1" />
              <span className="text-xs font-medium text-white">Rated 4.9/5 by over 500 customers</span>
            </div>
            
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-tight">
              <span className="block">Mobile Auto Diagnostics</span>
              <span className="text-automotive-orange">At Your Location</span>
            </h1>
            
            <p className="text-base sm:text-lg text-gray-200 max-w-xl lg:mx-0 mx-auto">
              Expert technicians bring professional diagnostic equipment to your home or office. 
              Save time and hassle with our convenient mobile service.
            </p>

            <div className="grid grid-cols-2 gap-4 pt-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-automotive-orange flex-shrink-0" />
                <span className="text-sm">No towing required</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-automotive-orange flex-shrink-0" />
                <span className="text-sm">Same-day appointments</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-automotive-orange flex-shrink-0" />
                <span className="text-sm">Professional equipment</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-automotive-orange flex-shrink-0" />
                <span className="text-sm">Certified technicians</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start pt-2">
              <Link to="/booking" className="w-full sm:w-auto">
                <Button
                  size="lg"
                  className="bg-automotive-orange hover:bg-automotive-orange/90 text-white px-6 py-2.5 text-base font-medium rounded-md shadow-lg hover:shadow-xl transition-all duration-300 w-full"
                >
                  Book Diagnostic Service
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
              <a href="tel:+254727795520" className="w-full sm:w-auto">
                <Button
                  variant="outline"
                  size="lg"
                  className="border border-white/30 text-white hover:bg-white/10 px-6 py-2.5 text-base font-medium rounded-md w-full"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Call for Emergency
                </Button>
              </a>
            </div>
          </div>

          {/* Right Content - Service Highlights */}
          <div className="lg:w-1/2 animate-fade-in-right">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-5 border border-white/20">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Car className="w-5 h-5 text-automotive-orange mr-2" />
                Our Diagnostic Services
              </h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div className="bg-white/10 rounded-lg p-3 hover:bg-white/20 transition-colors">
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 bg-automotive-blue/20 rounded-full flex items-center justify-center mr-2">
                      <Wrench className="w-4 h-4 text-automotive-orange" />
                    </div>
                    <h4 className="font-medium">Engine Diagnostics</h4>
                  </div>
                  <p className="text-xs text-gray-200">Comprehensive engine analysis with advanced OBD-II scanners</p>
                </div>
                
                <div className="bg-white/10 rounded-lg p-3 hover:bg-white/20 transition-colors">
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 bg-automotive-blue/20 rounded-full flex items-center justify-center mr-2">
                      <Shield className="w-4 h-4 text-automotive-orange" />
                    </div>
                    <h4 className="font-medium">Electrical Systems</h4>
                  </div>
                  <p className="text-xs text-gray-200">Battery testing, alternator analysis, and wiring inspection</p>
                </div>
                
                <div className="bg-white/10 rounded-lg p-3 hover:bg-white/20 transition-colors">
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 bg-automotive-blue/20 rounded-full flex items-center justify-center mr-2">
                      <Clock className="w-4 h-4 text-automotive-orange" />
                    </div>
                    <h4 className="font-medium">Brake Inspection</h4>
                  </div>
                  <p className="text-xs text-gray-200">Brake system evaluation including fluid analysis and ABS diagnostics</p>
                </div>
                
                <div className="bg-white/10 rounded-lg p-3 hover:bg-white/20 transition-colors">
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 bg-automotive-blue/20 rounded-full flex items-center justify-center mr-2">
                      <MapPin className="w-4 h-4 text-automotive-orange" />
                    </div>
                    <h4 className="font-medium">On-Site Service</h4>
                  </div>
                  <p className="text-xs text-gray-200">Professional diagnostics at your home, office or roadside</p>
                </div>
              </div>
              
              <div className="mt-4 pt-3 border-t border-white/20 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex -space-x-2">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="w-8 h-8 rounded-full bg-automotive-blue/30 border-2 border-white/20 flex items-center justify-center text-xs font-medium">
                        T{i}
                      </div>
                    ))}
                  </div>
                  <span className="text-xs ml-2">Our certified technicians</span>
                </div>
                <Link to="/services" className="text-xs text-automotive-orange hover:text-automotive-orange/80 font-medium flex items-center">
                  View all services
                  <ArrowRight className="w-3 h-3 ml-1" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;


