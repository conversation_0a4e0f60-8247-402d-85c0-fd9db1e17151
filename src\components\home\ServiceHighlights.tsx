
"use client";

import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Wrench, 
  Car, 
  Battery, 
  Gauge, 
  Settings, 
  Shield, 
  Clock, 
  CheckCircle,
  AlertTriangle,
  Zap,
  Thermometer,
  Fuel,
  ArrowRight,
  Phone
} from "lucide-react";

const ServiceHighlights = () => {
  const services = [
    {
      icon: Car,
      title: "Engine Diagnostics",
      description: "Comprehensive engine analysis using advanced OBD-II scanners",
      price: "From $89",
      category: "Diagnostics",
      color: "bg-automotive-blue",
      popular: true
    },
    {
      icon: Battery,
      title: "Electrical System",
      description: "Complete electrical system diagnostics including battery testing",
      price: "From $75",
      category: "Electrical",
      color: "bg-automotive-orange"
    },
    {
      icon: Gauge,
      title: "Brake System",
      description: "Thorough brake system evaluation including ABS diagnostics",
      price: "From $65",
      category: "Safety",
      color: "bg-automotive-orange"
    },
    {
      icon: Settings,
      title: "Transmission",
      description: "Advanced transmission system analysis and performance evaluation",
      price: "From $95",
      category: "Drivetrain",
      color: "bg-automotive-blue"
    },
    {
      icon: Thermometer,
      title: "Cooling System",
      description: "Complete cooling system diagnostics and leak detection",
      price: "From $70",
      category: "Engine",
      color: "bg-automotive-blue"
    },
    {
      icon: Fuel,
      title: "Fuel System",
      description: "Comprehensive fuel system diagnostics and injector testing",
      price: "From $80",
      category: "Performance",
      color: "bg-automotive-blue"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-10">
          <Badge variant="outline" className="border-automotive-blue text-automotive-blue mb-3">
            Our Services
          </Badge>
          <h2 className="text-3xl font-bold text-automotive-dark mb-4">
            Comprehensive Auto
            <span className="text-automotive-orange"> Diagnostic Services</span>
          </h2>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto">
            Professional mobile automotive diagnostics using state-of-the-art equipment.
            Our certified technicians bring expertise directly to your location.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
          {services.map((service, index) => (
            <Card key={index} className="group border border-border hover:border-automotive-blue transition-all duration-300 h-full">
              <CardContent className="p-4 flex flex-col h-full">
                <div className="flex items-start mb-3">
                  <div className={`p-2 rounded-lg ${service.color === 'bg-automotive-blue' ? 'bg-automotive-blue/10' : 'bg-automotive-orange/10'} mr-3`}>
                    <service.icon className={`w-5 h-5 ${service.color === 'bg-automotive-blue' ? 'text-automotive-blue' : 'text-automotive-orange'}`} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-automotive-dark text-sm group-hover:text-automotive-blue transition-colors">
                      {service.title}
                    </h3>
                    <Badge variant="secondary" className="text-xs mt-1 font-normal">
                      {service.category}
                    </Badge>
                  </div>
                </div>
                
                <p className="text-xs text-gray-600 leading-relaxed mb-3 flex-grow">
                  {service.description}
                </p>
                
                <div className="flex items-center justify-between mt-auto pt-3 border-t border-border">
                  <span className="text-sm font-medium text-automotive-dark">{service.price}</span>
                  {service.popular && (
                    <Badge className="bg-automotive-orange text-white text-xs">
                      Popular
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Services */}
        <div className="text-center">
          <Link to="/services">
            <Button variant="outline" className="border-automotive-blue text-automotive-blue hover:bg-automotive-blue hover:text-white">
              View All Services & Pricing
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </Link>
        </div>

        {/* Emergency Services Banner */}
        <div className="bg-gradient-to-r from-automotive-dark to-automotive-blue rounded-xl p-6 text-white mt-12">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="flex items-center md:items-start">
              <AlertTriangle className="w-10 h-10 text-automotive-orange mr-4 flex-shrink-0" />
              <div>
                <h3 className="text-xl font-bold mb-1">24/7 Emergency Diagnostic Services</h3>
                <p className="text-sm text-gray-200">
                  Vehicle breakdown? Don't wait! Our emergency diagnostic team is available around the clock.
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button 
                size="sm" 
                className="bg-automotive-orange hover:bg-automotive-orange/90 text-white whitespace-nowrap" 
                asChild
              >
                <a href="tel:+254727795520">
                  <Phone className="w-4 h-4 mr-2" />
                  Call: 0727 795 520
                </a>
              </Button>
              <Button variant="outline" size="sm" className="border-white/20 text-white hover:bg-white/10 whitespace-nowrap">
                Request Assistance
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServiceHighlights;



