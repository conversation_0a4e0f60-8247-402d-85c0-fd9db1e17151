
"use client";

import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { ArrowRight, Calendar, Clock, MapPin, CheckCircle, PhoneCall } from "lucide-react";

const BookingCTA = () => {
  const benefits = [
    { icon: Clock, text: "Fast 30-minute response time" },
    { icon: MapPin, text: "Service at your location" },
    { icon: Calendar, text: "Available 7 days a week" },
    { icon: CheckCircle, text: "100% satisfaction guarantee" }
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with overlay */}
      <div className="absolute inset-0 bg-automotive-dark z-0">
        <div className="absolute inset-0 bg-gradient-to-r from-automotive-blue/90 to-automotive-dark/95 z-10"></div>
        {/* Add subtle pattern overlay */}
        <div className="absolute inset-0 bg-[url('/patterns/circuit-board.svg')] opacity-5 z-20"></div>
      </div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-30">
        <div className="max-w-5xl mx-auto">
          <motion.div 
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            {/* Decorative element */}
            <div className="w-24 h-1 bg-gradient-to-r from-automotive-orange to-automotive-orange/50 mx-auto mb-8 rounded-full"></div>
            
            <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-6 px-3 py-1">
              Professional Diagnostics
            </Badge>
            
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Ready to Get Your Vehicle <span className="text-automotive-orange">Diagnosed Today?</span>
            </h2>
            
            <p className="text-xl text-white/80 mb-10 max-w-3xl mx-auto leading-relaxed">
              Our mobile technicians bring professional diagnostic equipment directly to your location. 
              Book now and experience the convenience of DiagOnWheels.
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-5 justify-center pt-4">
              <Link to="/booking">
                <Button
                  size="lg"
                  className="bg-automotive-orange hover:bg-automotive-orange/90 text-white px-10 py-7 text-xl rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  Book Diagnostic Service
                  <ArrowRight className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform" />
                </Button>
              </Link>
              <a href="tel:+254727795520">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/40 text-white hover:bg-white/10 px-10 py-7 text-xl rounded-full shadow-md"
                >
                  <PhoneCall className="w-5 h-5 mr-3" />
                  Call for Assistance
                </Button>
              </a>
            </div>
            
            {/* Trust indicators */}
            <div className="mt-12 flex flex-wrap justify-center gap-6 items-center">
              <div className="flex items-center text-white/70">
                <CheckCircle className="w-5 h-5 mr-2 text-automotive-orange" />
                <span>No-Obligation Quote</span>
              </div>
              <div className="flex items-center text-white/70">
                <CheckCircle className="w-5 h-5 mr-2 text-automotive-orange" />
                <span>Same-Day Service</span>
              </div>
              <div className="flex items-center text-white/70">
                <CheckCircle className="w-5 h-5 mr-2 text-automotive-orange" />
                <span>Certified Technicians</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default BookingCTA;





