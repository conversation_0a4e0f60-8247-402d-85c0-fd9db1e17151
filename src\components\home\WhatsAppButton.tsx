
"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MessageCircle, X } from "lucide-react";

const WhatsAppButton = () => {
  const [isVisible, setIsVisible] = useState(true);

  const handleWhatsAppClick = () => {
    const phoneNumber = "254727795520"; // Updated WhatsApp number
    const message = encodeURIComponent("Hi! I'm interested in your mobile automotive diagnostic services. Can you help me schedule an appointment?");
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="relative">
        {/* Close button */}
        <button
          onClick={() => setIsVisible(false)}
          className="absolute -top-2 -right-2 w-6 h-6 bg-secondary hover:bg-secondary/80 text-white rounded-full flex items-center justify-center text-xs transition-colors z-10"
        >
          <X className="w-3 h-3" />
        </button>

        {/* WhatsApp Button */}
        <Button
          onClick={handleWhatsAppClick}
          className="bg-automotive-orange hover:bg-automotive-orange/90 text-white rounded-full w-16 h-16 p-0 shadow-lg hover:shadow-xl transition-all duration-300 group"
          title="Chat with us on WhatsApp"
        >
          <MessageCircle className="w-8 h-8 group-hover:scale-110 transition-transform" />
        </Button>

        {/* Pulse animation */}
        <div className="absolute inset-0 bg-automotive-orange rounded-full animate-ping opacity-75"></div>

        {/* Tooltip */}
        <div className="absolute bottom-full right-0 mb-2 bg-automotive-dark text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          Chat with us on WhatsApp
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-l-transparent border-r-4 border-r-transparent border-t-4 border-t-automotive-dark"></div>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppButton;

