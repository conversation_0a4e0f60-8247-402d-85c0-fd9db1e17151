import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { CheckCircle, Phone, CalendarDays, Wrench } from "lucide-react";

const HowItWorks = () => {
  const steps = [
    {
      id: 1,
      icon: <Phone className="w-10 h-10 text-automotive-orange mb-4" />,
      title: "Book Your Service",
      description:
        "Easily book your diagnostic service online or by calling us. Provide your vehicle details and preferred time.",
      bgColor: "bg-muted",
    },
    {
      id: 2,
      icon: <CalendarDays className="w-10 h-10 text-automotive-blue mb-4" />,
      title: "We Come To You",
      description:
        "Our certified technician arrives at your location (home, office, or roadside) at the scheduled time with all necessary equipment.",
      bgColor: "bg-background",
    },
    {
      id: 3,
      icon: <Wrench className="w-10 h-10 text-automotive-blue mb-4" />,
      title: "Expert Diagnostics & Repair",
      description:
        "We perform a thorough diagnostic check, explain the findings, and provide a transparent quote. With your approval, we can often perform repairs on the spot.",
      bgColor: "bg-muted",
    },
    {
      id: 4,
      icon: <CheckCircle className="w-10 h-10 text-automotive-orange mb-4" />,
      title: "Drive with Confidence",
      description:
        "Once the service is complete, we ensure everything is working perfectly. You pay securely and get back on the road with peace of mind.",
      bgColor: "bg-background",
    },
  ];

  return (
    <div className="py-16 sm:py-24 bg-gradient-to-b from-background to-muted">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-automotive-dark mb-4">
            How Our Mobile Diagnostics Work
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Getting your vehicle diagnosed and repaired has never been easier.
            Follow these simple steps.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-10">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`relative flex flex-col items-center p-8 rounded-2xl shadow-lg transition-all duration-300 hover:shadow-xl ${step.bgColor}`}
            >
              <div className="absolute -top-6">
                <div className={`w-14 h-14 rounded-full flex items-center justify-center shadow-md ${
                    index % 2 === 0 ? "bg-automotive-blue" : "bg-automotive-orange"
                } text-white text-2xl font-bold border-4 border-white`}>
                  {step.id}
                </div>
              </div>
              <div className="mt-8 mb-6">
                {step.icon}
              </div>
              <h3 className="text-2xl font-bold text-automotive-dark mb-3">
                {step.title}
              </h3>
              <p className="text-gray-600 text-center leading-relaxed">
                {step.description}
              </p>
            </div>
          ))}
        </div>

        <div className="text-center mt-20">
          <p className="text-xl text-foreground mb-6 font-medium">
            Ready to experience hassle-free auto care?
          </p>
          <Link to="/booking">
            <Button
              size="lg"
              className="bg-gradient-to-r from-automotive-orange to-automotive-orange hover:from-automotive-orange/90 hover:to-automotive-orange/90 text-white px-10 py-7 text-lg font-semibold shadow-2xl hover:shadow-automotive-orange/30 transition-all duration-300 rounded-xl group"
            >
              Book Your Diagnostic Now
              <CheckCircle className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HowItWorks;