
"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Zap, 
  Clock, 
  MapPin, 
  Shield, 
  Wrench, 
  Smartphone,
  CreditCard,
  Award,
  CheckCircle
} from "lucide-react";

const FeatureSection = () => {
  const features = [
    {
      icon: Zap,
      title: "Fast Response",
      description: "Quick 15-30 minute arrival time to your location across the metro area",
      color: "bg-automotive-blue"
    },
    {
      icon: Wrench,
      title: "Advanced Equipment",
      description: "Professional-grade diagnostic tools that match dealership capabilities",
      color: "bg-automotive-orange"
    },
    {
      icon: Shield,
      title: "Certified Technicians",
      description: "ASE-certified mechanics with extensive training and experience",
      color: "bg-automotive-blue"
    },
    {
      icon: MapPin,
      title: "Mobile Convenience",
      description: "We come to your home, office, or roadside - no towing needed",
      color: "bg-automotive-orange"
    },
    {
      icon: Smartphone,
      title: "Digital Reports",
      description: "Detailed diagnostic reports sent directly to your email or phone",
      color: "bg-automotive-blue"
    },
    {
      icon: Credit<PERSON>ard,
      title: "Transparent Pricing",
      description: "Clear, upfront pricing with no hidden fees or surprise charges",
      color: "bg-automotive-orange"
    }
  ];

  return (
    <section className="py-16 bg-automotive-gray/5">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-10">
          <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-3">
            Why Choose Us
          </Badge>
          <h2 className="text-3xl font-bold text-automotive-dark mb-4">
            The <span className="text-automotive-blue">DiagOnWheels</span> Advantage
          </h2>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto">
            Experience the convenience and expertise of our mobile diagnostic service
            with benefits designed around your needs.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="border border-border/50 hover:border-automotive-blue/50 hover:shadow-md transition-all duration-300"
            >
              <CardHeader className="pb-2">
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 ${feature.color} rounded-lg flex items-center justify-center`}>
                    <feature.icon className="w-5 h-5 text-white" />
                  </div>
                  <CardTitle className="text-lg font-semibold text-automotive-dark">
                    {feature.title}
                  </CardTitle>
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 pt-8 border-t border-border/50">
          <div className="flex flex-wrap justify-center gap-8">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-automotive-blue" />
              <span className="text-sm font-medium">5,000+ Satisfied Customers</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-automotive-blue" />
              <span className="text-sm font-medium">ASE Certified Technicians</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-automotive-blue" />
              <span className="text-sm font-medium">Insured & Licensed</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-automotive-orange" />
              <span className="text-sm font-medium">Top-Rated Service Provider</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureSection;

