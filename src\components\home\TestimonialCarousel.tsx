
"use client";

import React, { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight, Quote, Star } from "lucide-react";

const TestimonialCarousel = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      location: "Downtown Metro",
      rating: 5,
      text: "DiagOnWheels saved me so much time! The technician came to my office parking lot and diagnosed my car's issue in 30 minutes. Professional service and fair pricing.",
      service: "Engine Diagnostics",
      avatar: "SJ"
    },
    {
      name: "<PERSON>", 
      location: "Residential Area",
      rating: 5,
      text: "My car broke down on the highway and DiagOnWheels was there within 20 minutes. They quickly identified the problem and got me back on the road. Highly recommend!",
      service: "Emergency Roadside",
      avatar: "MR"
    },
    {
      name: "<PERSON>",
      location: "Business District", 
      rating: 5,
      text: "The mobile service was incredibly convenient. The technician was knowledgeable, explained everything clearly, and provided a detailed report. Great experience overall!",
      service: "Electrical System",
      avatar: "EC"
    },
    {
      name: "<PERSON>",
      location: "Suburbs",
      rating: 5,
      text: "Outstanding service! They diagnosed my transmission issue at home while I was working. The report was thorough and the pricing was transparent. Will use again!",
      service: "Transmission Diagnostics",
      avatar: "DT"
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [autoplay, setAutoplay] = useState(true);

  const nextTestimonial = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setDirection(-1);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  useEffect(() => {
    if (!autoplay) return;
    
    const interval = setInterval(() => {
      nextTestimonial();
    }, 5000);
    
    return () => clearInterval(interval);
  }, [autoplay, currentIndex]);

  const variants = {
    enter: (direction) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction) => ({
      x: direction < 0 ? 300 : -300,
      opacity: 0
    })
  };

  return (
    <section className="py-24 bg-gradient-to-b from-white to-automotive-gray/10">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-4 px-3 py-1 text-sm font-medium">
            Customer Reviews
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold text-automotive-dark mb-6">
            What Our Customers
            <span className="text-automotive-blue"> Say About Us</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Don't just take our word for it. Here's what real customers say about
            their experience with DiagOnWheels mobile automotive services.
          </p>
        </div>

        {/* Testimonial Carousel */}
        <div className="relative max-w-4xl mx-auto"
          onMouseEnter={() => setAutoplay(false)}
          onMouseLeave={() => setAutoplay(true)}
        >
          <Card className="border-0 shadow-xl overflow-hidden bg-white rounded-2xl relative">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-automotive-blue to-automotive-orange"></div>
            <div className="absolute top-8 right-8 opacity-10">
              <Quote className="w-24 h-24 text-automotive-orange" />
            </div>
            
            <CardContent className="p-8 md:p-12 relative min-h-[400px]">
              <AnimatePresence initial={false} custom={direction} mode="wait">
                <motion.div
                  key={currentIndex}
                  custom={direction}
                  variants={variants}
                  initial="enter"
                  animate="center"
                  exit="exit"
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  className="text-center space-y-8"
                >
                  {/* Stars */}
                  <div className="flex justify-center space-x-1">
                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                      <Star key={i} className="w-6 h-6 fill-automotive-orange text-automotive-orange" />
                    ))}
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="text-xl md:text-2xl text-foreground leading-relaxed italic max-w-3xl mx-auto font-light">
                    "{testimonials[currentIndex].text}"
                  </blockquote>

                  {/* Customer Info */}
                  <div className="flex flex-col items-center space-y-4">
                    <Avatar className="w-20 h-20 bg-gradient-to-br from-automotive-blue to-automotive-blue/80 text-white shadow-lg">
                      <AvatarFallback className="text-lg font-bold">
                        {testimonials[currentIndex].avatar}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="text-center">
                      <div className="font-bold text-automotive-dark text-xl">
                        {testimonials[currentIndex].name}
                      </div>
                      <div className="text-muted-foreground">
                        {testimonials[currentIndex].location}
                      </div>
                      <Badge variant="secondary" className="mt-3 bg-automotive-blue/10 text-automotive-blue px-3 py-1">
                        {testimonials[currentIndex].service}
                      </Badge>
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-center items-center space-x-6 mt-10">
            <Button
              variant="outline"
              size="icon"
              onClick={prevTestimonial}
              className="rounded-full w-14 h-14 border-2 border-automotive-blue text-automotive-blue hover:bg-automotive-blue hover:text-white transition-all duration-300 shadow-sm"
            >
              <ChevronLeft className="w-6 h-6" />
            </Button>

            {/* Dots Indicator */}
            <div className="flex space-x-3">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setDirection(index > currentIndex ? 1 : -1);
                    setCurrentIndex(index);
                  }}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-automotive-blue scale-125'
                      : 'bg-muted hover:bg-muted-foreground/50'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>

            <Button
              variant="outline"
              size="icon"
              onClick={nextTestimonial}
              className="rounded-full w-14 h-14 border-2 border-automotive-blue text-automotive-blue hover:bg-automotive-blue hover:text-white transition-all duration-300 shadow-sm"
            >
              <ChevronRight className="w-6 h-6" />
            </Button>
          </div>
        </div>

        {/* Overall Rating */}
        <div className="text-center mt-20">
          <motion.div 
            className="bg-white rounded-2xl p-8 max-w-2xl mx-auto shadow-xl border border-automotive-blue/10"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="text-3xl font-bold text-automotive-dark mb-3">4.9/5 Average Rating</div>
            <div className="flex justify-center space-x-1 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-7 h-7 fill-automotive-orange text-automotive-orange" />
              ))}
            </div>
            <p className="text-muted-foreground">
              Based on 2,500+ reviews from satisfied customers across the metro area
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialCarousel;



