"use client";

import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  ArrowRight, 
  Car, 
  <PERSON>anLine, 
  CheckCircle, 
  <PERSON>fresh<PERSON><PERSON><PERSON>, 
  FileSearch, 
  Clock, 
  Filter, 
  Truck, 
  Zap, 
  AlertTriangle, 
  Send,
  Wrench,
  Gauge,
  Battery
} from "lucide-react";
import ServiceCoverage from "@/components/home/<USER>";
import WhatsAppButton from "@/components/home/<USER>";
import { motion } from "framer-motion";

// Define service type
interface Service {
  id: number;
  icon: React.ElementType;
  title: string;
  description: string;
  features: string[];
  price: string;
  color: string;
  category: string;
  estimatedTime: string;
  compatibility: string[];
  popular?: boolean;
}

const Services = () => {
  const [activeFilter, setActiveFilter] = useState("all");
  const [priceSort, setPriceSort] = useState("default");
  const [filteredServices, setFilteredServices] = useState<Service[]>([]);
  const [quoteService, setQuoteService] = useState<Service | null>(null);
  const [quoteFormData, setQuoteFormData] = useState({
    name: "",
    email: "",
    phone: "",
    vehicleInfo: "",
    message: ""
  });

  const services: Service[] = [
    {
      id: 1,
      icon: Car,
      title: "Engine Diagnostics",
      description: "Accurate scanning of engine-related faults using advanced tools.",
      features: [
        "Check Engine Light Diagnosis",
        "Engine Misfire & Idle Issue Detection",
        "Fuel System Scan",
        "Live Engine Data Readout",
      ],
      price: "KES 2,000",
      color: "bg-automotive-blue",
      category: "Engine",
      estimatedTime: "30-45 minutes",
      compatibility: ["Passenger Cars", "SUVs", "Light Trucks"],
      popular: true
    },
    {
      id: 2,
      icon: ScanLine,
      title: "Full System Scan",
      description: "A complete health check of all electronic control units (ECUs).",
      features: [
        "ABS, SRS, Transmission, and BCM Scan",
        "Fault Code Retrieval & Clearing",
        "Live Data Stream Access",
        "System Health Summary Report",
      ],
      price: "KES 3,000",
      color: "bg-automotive-orange",
      category: "Comprehensive",
      estimatedTime: "45-60 minutes",
      compatibility: ["All Vehicle Types"],
      popular: true
    },
    {
      id: 3,
      icon: RefreshCcw,
      title: "Service Light Reset",
      description: "Reset dashboard service reminders after regular maintenance.",
      features: [
        "Oil Change Indicator Reset",
        "Inspection Countdown Reset",
        "Custom Service Reminder Adjustment",
      ],
      price: "KES 800",
      color: "bg-secondary",
      category: "Maintenance",
      estimatedTime: "15-20 minutes",
      compatibility: ["Passenger Cars", "SUVs", "Light Trucks"]
    },
    {
      id: 4,
      icon: FileSearch,
      title: "OBD Fault Code Interpretation",
      description: "Quick and affordable fault code scanning with expert feedback.",
      features: [
        "Plug-in Scan",
        "Code Meaning Explanation",
        "Recommendation on Next Steps",
      ],
      price: "KES 1,000",
      color: "bg-automotive-blue",
      category: "Basic",
      estimatedTime: "20-30 minutes",
      compatibility: ["OBD-II Compatible Vehicles"]
    },
    {
      id: 5,
      icon: Battery,
      title: "Electrical System Diagnostics",
      description: "Comprehensive testing of your vehicle's electrical components.",
      features: [
        "Battery Health Analysis",
        "Alternator Output Testing",
        "Starter Motor Assessment",
        "Electrical Circuit Testing"
      ],
      price: "KES 1,500",
      color: "bg-automotive-orange",
      category: "Electrical",
      estimatedTime: "30-45 minutes",
      compatibility: ["All Vehicle Types"]
    },
    {
      id: 6,
      icon: Gauge,
      title: "Brake System Diagnostics",
      description: "Thorough inspection of brake system components and performance.",
      features: [
        "ABS System Scan",
        "Brake Pressure Testing",
        "Electronic Parking Brake Diagnostics",
        "Brake Pad Sensor Check"
      ],
      price: "KES 1,800",
      color: "bg-secondary",
      category: "Safety",
      estimatedTime: "30-40 minutes",
      compatibility: ["Passenger Cars", "SUVs", "Light Trucks"]
    },
    {
      id: 7,
      icon: Wrench,
      title: "Transmission Diagnostics",
      description: "In-depth analysis of automatic and manual transmission systems.",
      features: [
        "Transmission Control Module Scan",
        "Shift Pattern Analysis",
        "Fluid Pressure Testing",
        "Solenoid Function Check"
      ],
      price: "KES 2,500",
      color: "bg-automotive-blue",
      category: "Drivetrain",
      estimatedTime: "40-60 minutes",
      compatibility: ["Passenger Cars", "SUVs", "Light Trucks"]
    },
    {
      id: 8,
      icon: Zap,
      title: "Emergency Diagnostic Service",
      description: "Urgent diagnostic service for vehicles with critical issues.",
      features: [
        "Priority Response",
        "Critical System Scan",
        "Emergency Repair Recommendations",
        "Towing Coordination if Needed"
      ],
      price: "KES 4,000",
      color: "bg-automotive-orange",
      category: "Emergency",
      estimatedTime: "ASAP (15-30 minutes response)",
      compatibility: ["All Vehicle Types"]
    },
  ];

  // FAQ data
  const faqs = [
    {
      question: "How long does a typical diagnostic service take?",
      answer: "Most diagnostic services take between 30-60 minutes depending on the complexity of the issue and the type of vehicle. Basic scans can be completed in as little as 15 minutes, while comprehensive system diagnostics may take up to an hour."
    },
    {
      question: "Do I need to be present during the diagnostic service?",
      answer: "While it's not mandatory, we recommend being present so our technician can explain the findings directly. However, if you're unable to be present, we can send a detailed report via email or WhatsApp."
    },
    {
      question: "What information will I receive after the diagnostic?",
      answer: "You'll receive a comprehensive report that includes all fault codes found, their meanings, affected systems, and our technician's recommendations for repairs. We also provide an estimate of repair costs if requested."
    },
    {
      question: "Can you diagnose any make and model of vehicle?",
      answer: "We can diagnose most vehicles manufactured after 1996 (when OBD-II became standard). Our equipment supports European, Asian, and American vehicles. For exotic or very specialized vehicles, please contact us in advance to confirm compatibility."
    },
    {
      question: "Will the diagnostic service fix my vehicle's problem?",
      answer: "The diagnostic service identifies the problem but doesn't include repairs. Once we've diagnosed the issue, we can provide repair recommendations and, in some cases, perform simple fixes on the spot for an additional fee."
    }
  ];

  // Filter services based on category and sort by price
  useEffect(() => {
    let result = [...services];
    
    // Apply category filter
    if (activeFilter !== "all") {
      result = result.filter(service => service.category === activeFilter);
    }
    
    // Apply price sorting
    if (priceSort === "lowToHigh") {
      result = result.sort((a, b) => {
        const priceA = parseInt(a.price.replace(/\D/g, ''));
        const priceB = parseInt(b.price.replace(/\D/g, ''));
        return priceA - priceB;
      });
    } else if (priceSort === "highToLow") {
      result = result.sort((a, b) => {
        const priceA = parseInt(a.price.replace(/\D/g, ''));
        const priceB = parseInt(b.price.replace(/\D/g, ''));
        return priceB - priceA;
      });
    }
    
    setFilteredServices(result);
  }, [activeFilter, priceSort]);

  // Handle quote form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setQuoteFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle quote form submission
  const handleQuoteSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would send this data to your backend
    console.log("Quote requested for:", quoteService?.title);
    console.log("Form data:", quoteFormData);
    
    // Reset form
    setQuoteFormData({
      name: "",
      email: "",
      phone: "",
      vehicleInfo: "",
      message: ""
    });
    
    // Close dialog (would be handled by the Dialog component's onOpenChange)
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  // Get unique categories for filter
  const categories = ["all", ...Array.from(new Set(services.map(service => service.category)))];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-automotive-dark to-automotive-blue text-white py-20">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-6">
                Professional Services
              </Badge>
              <h1 className="text-4xl lg:text-6xl font-bold mb-6">
                Complete Mobile
                <span className="text-automotive-orange"> Diagnostic Services</span>
              </h1>
              <p className="text-xl text-primary-foreground mb-8 leading-relaxed">
                From engine troubles to electrical issues, our certified technicians provide comprehensive diagnostic services at your location with state-of-the-art equipment.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-automotive-orange hover:bg-automotive-orange/90">
                  <Link to="/booking">
                    Book Service Now
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-automotive-dark">
                  <Link to="/contact">Get Quote</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Services Section with Filtering */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge variant="outline" className="border-automotive-blue text-automotive-blue mb-4">
                Professional Services
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold text-automotive-dark mb-4">
                Our Diagnostic
                <span className="text-automotive-orange"> Service Packages</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Choose from our range of professional diagnostic services tailored to your vehicle's needs.
              </p>
            </div>

            {/* Filtering and Sorting Controls */}
            <div className="bg-gray-50 rounded-xl p-6 mb-12 shadow-sm">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                <div className="w-full md:w-auto">
                  <div className="flex items-center gap-2 mb-3">
                    <Filter className="w-4 h-4 text-automotive-blue" />
                    <span className="text-sm font-medium text-automotive-dark">Filter by category:</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {categories.map((category) => (
                      <Badge 
                        key={category}
                        variant={activeFilter === category ? "default" : "outline"}
                        className={`cursor-pointer px-3 py-1.5 capitalize ${
                          activeFilter === category 
                            ? "bg-automotive-blue hover:bg-automotive-blue/90" 
                            : "hover:bg-muted border-gray-300"
                        }`}
                        onClick={() => setActiveFilter(category)}
                      >
                        {category === "all" ? "All Services" : category}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="w-full md:w-auto">
                  <div className="flex items-center gap-2 mb-3">
                    <ArrowRight className="w-4 h-4 text-automotive-blue rotate-90" />
                    <span className="text-sm font-medium text-automotive-dark">Sort by price:</span>
                  </div>
                  <Tabs defaultValue="default" value={priceSort} onValueChange={setPriceSort} className="w-full">
                    <TabsList className="w-full bg-white border border-gray-200">
                      <TabsTrigger value="default" className="flex-1">Default</TabsTrigger>
                      <TabsTrigger value="lowToHigh" className="flex-1">Low to High</TabsTrigger>
                      <TabsTrigger value="highToLow" className="flex-1">High to Low</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </div>
            </div>

            {/* Services Grid */}
            <motion.div 
              className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {filteredServices.map((service) => (
                <motion.div key={service.id} variants={itemVariants}>
                  <Card
                    className="group h-full border border-gray-100 hover:border-gray-200 shadow-sm hover:shadow-md transition-all duration-300"
                  >
                    <div className="relative">
                      <div className={`absolute top-0 left-0 w-full h-0.5 ${service.color}`}></div>
                      {service.popular && (
                        <Badge className="absolute top-3 right-3 bg-automotive-orange text-white text-xs py-0 px-2">
                          Popular
                        </Badge>
                      )}
                    </div>
                    
                    <CardHeader className="pt-4 pb-2 px-4">
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 ${service.color} bg-opacity-10 rounded-md flex items-center justify-center flex-shrink-0`}>
                          <service.icon className={`w-4 h-4 ${service.color === 'bg-automotive-blue' ? 'text-automotive-blue' : service.color === 'bg-automotive-orange' ? 'text-automotive-orange' : 'text-gray-500'}`} />
                        </div>
                        <div>
                          <CardTitle className="text-base font-bold text-automotive-dark group-hover:text-automotive-blue transition-colors">
                            {service.title}
                          </CardTitle>
                          <p className="text-xs text-muted-foreground">
                            {service.category}
                          </p>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="px-4 py-2">
                      <div className="flex items-center justify-between text-xs mb-3">
                        <div className="flex items-center text-muted-foreground">
                          <Clock className="w-3 h-3 mr-1 text-automotive-blue" />
                          {service.estimatedTime}
                        </div>
                        <div className="text-lg font-bold text-automotive-dark">{service.price}</div>
                      </div>
                      
                      <div className="space-y-1 mb-3">
                        {service.features.slice(0, 3).map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-start space-x-1.5">
                            <CheckCircle className="w-3 h-3 text-automotive-blue flex-shrink-0 mt-0.5" />
                            <span className="text-xs text-gray-600">{feature}</span>
                          </div>
                        ))}
                        {service.features.length > 3 && (
                          <div className="text-xs text-automotive-blue font-medium pl-4.5">
                            +{service.features.length - 3} more features
                          </div>
                        )}
                      </div>
                      
                      <div className="flex flex-wrap gap-1 mb-3">
                        {service.compatibility.slice(0, 2).map((vehicle, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs bg-gray-50 font-normal py-0 px-1.5">
                            {vehicle}
                          </Badge>
                        ))}
                        {service.compatibility.length > 2 && (
                          <Badge variant="outline" className="text-xs bg-gray-50 font-normal py-0 px-1.5">
                            +{service.compatibility.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </CardContent>

                    <CardFooter className="px-4 py-3 bg-gray-50 border-t border-gray-100 flex gap-2">
                      <Button 
                        size="sm"
                        variant="outline"
                        className="text-xs px-2 py-1 h-8 border-automotive-blue text-automotive-blue hover:bg-automotive-blue hover:text-white"
                        onClick={() => setQuoteService(service)}
                      >
                        Request Quote
                      </Button>
                      <Button 
                        size="sm"
                        className="text-xs px-2 py-1 h-8 bg-automotive-orange hover:bg-automotive-orange/90 text-white ml-auto"
                        asChild
                      >
                        <Link to="/booking">
                          Book Now
                          <ArrowRight className="w-3 h-3 ml-1" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
            
            {/* No Results Message */}
            {filteredServices.length === 0 && (
              <div className="text-center py-12">
                <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
                  <FileSearch className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-automotive-dark mb-2">No services found</h3>
                  <p className="text-muted-foreground mb-4">
                    No services match your current filter criteria.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={() => setActiveFilter("all")}
                    className="border-automotive-blue text-automotive-blue hover:bg-automotive-blue/5"
                  >
                    View All Services
                  </Button>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Service Coverage Section */}
        <ServiceCoverage />
      </main>
      
      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default Services;




