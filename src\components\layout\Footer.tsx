
"use client";

import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Facebook, 
  Twitter, 
  Instagram, 
  Shield,
  Award,
  Users,
  Settings,
  Linkedin,
  ChevronRight
} from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-automotive-dark pt-16 pb-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-16">
          {/* Company Info */}
          <div className="space-y-6">
            <div className="flex items-center">
              <img src="/logo-white.svg" alt="DiagOnWheels" className="h-10" />
            </div>
            <p className="text-white/70 leading-relaxed">
              Professional mobile automotive diagnostic services. We bring expert technicians and advanced equipment directly to your location.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 rounded-full bg-automotive-blue/20 flex items-center justify-center text-white hover:bg-automotive-blue transition-colors duration-300">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 rounded-full bg-automotive-blue/20 flex items-center justify-center text-white hover:bg-automotive-blue transition-colors duration-300">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 rounded-full bg-automotive-blue/20 flex items-center justify-center text-white hover:bg-automotive-blue transition-colors duration-300">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 rounded-full bg-automotive-blue/20 flex items-center justify-center text-white hover:bg-automotive-blue transition-colors duration-300">
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-automotive-orange">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-white hover:text-automotive-orange transition-colors duration-200 flex items-center">
                  <ChevronRight className="w-4 h-4 mr-2 text-automotive-blue" />
                  Home
                </Link>
              </li>
              <li>
                <Link to="/services" className="text-white hover:text-automotive-orange transition-colors duration-200 flex items-center">
                  <ChevronRight className="w-4 h-4 mr-2 text-automotive-blue" />
                  Services
                </Link>
              </li>
              <li>
                <Link to="/how-it-works" className="text-white hover:text-automotive-orange transition-colors duration-200 flex items-center">
                  <ChevronRight className="w-4 h-4 mr-2 text-automotive-blue" />
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-white hover:text-automotive-orange transition-colors duration-200 flex items-center">
                  <ChevronRight className="w-4 h-4 mr-2 text-automotive-blue" />
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-white hover:text-automotive-orange transition-colors duration-200 flex items-center">
                  <ChevronRight className="w-4 h-4 mr-2 text-automotive-blue" />
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-white hover:text-automotive-orange transition-colors duration-200 flex items-center">
                  <ChevronRight className="w-4 h-4 mr-2 text-automotive-blue" />
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-automotive-orange">Contact Info</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-automotive-blue mt-1" />
                <div>
                  <a href="tel:+254727795520" className="text-white font-medium hover:text-automotive-orange transition-colors">0727 795 520</a>
                  <p className="text-white/60 text-sm">24/7 Emergency Line</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-automotive-blue mt-1" />
                <div>
                  <a href="mailto:<EMAIL>" className="text-white hover:text-automotive-orange transition-colors"><EMAIL></a>
                  <p className="text-white/60 text-sm">Get in touch</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-automotive-blue mt-1" />
                <div>
                  <p className="text-white">Nairobi, Kenya</p>
                  <p className="text-white/60 text-sm">We come to you</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-automotive-blue mt-1" />
                <div>
                  <p className="text-white">8:00 AM – 6:00 PM</p>
                  <p className="text-white/60 text-sm">Monday to Saturday</p>
                </div>
              </div>
            </div>
          </div>

          {/* Newsletter */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-automotive-orange">Newsletter</h4>
            <p className="text-white/70">
              Subscribe to our newsletter for tips, news and special offers.
            </p>
            <div className="space-y-3">
              <div className="relative">
                <input 
                  type="email" 
                  placeholder="Your email address" 
                  className="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-automotive-blue"
                />
                <Button className="absolute right-1 top-1 bg-automotive-blue hover:bg-automotive-blue/90 text-white h-9 px-4 rounded-md">
                  Subscribe
                </Button>
              </div>
              <p className="text-white/50 text-xs">
                By subscribing, you agree to our Privacy Policy and consent to receive updates.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white/50 text-sm mb-4 md:mb-0">
            © {new Date().getFullYear()} DiagOnWheels. All rights reserved.
          </p>
          <div className="flex space-x-6">
            <Link to="/privacy-policy" className="text-white/50 hover:text-white text-sm transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link to="/terms-of-service" className="text-white/50 hover:text-white text-sm transition-colors duration-200">
              Terms of Service
            </Link>
            <Link to="/cookie-policy" className="text-white/50 hover:text-white text-sm transition-colors duration-200">
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;



