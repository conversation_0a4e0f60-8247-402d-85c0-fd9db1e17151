
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Index from "@/pages/Index";
import Services from "@/pages/Services";
import BookingPage from "@/pages/BookingPage";
import HowItWorks from "@/pages/HowItWorks";
import Contact from "@/pages/Contact";
import FAQ from "@/pages/FAQ";
import Emergency from "@/pages/Emergency";
import ScrollToTop from "@/components/layout/ScrollToTop";

function App() {
  return (
    <Router>
      {/* ScrollToTop component will handle scrolling to top on route changes */}
      <ScrollToTop />
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/services" element={<Services />} />
        <Route path="/booking" element={<BookingPage />} />
        <Route path="/how-it-works" element={<HowItWorks />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/faq" element={<FAQ />} />
        <Route path="/emergency" element={<Emergency />} />
      </Routes>
    </Router>
  );
}

export default App;


