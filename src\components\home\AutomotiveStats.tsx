
"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Car, 
  Clock, 
  Star, 
  Shield, 
  Wrench, 
  Calendar,
  MapPin 
} from "lucide-react";

const AutomotiveStats = () => {
  const stats = [
    {
      icon: Car,
      number: "10,000+",
      label: "Vehicles Diagnosed",
      description: "Successfully diagnosed and repaired",
      color: "text-automotive-blue"
    },
    {
      icon: Users,
      number: "5,000+",
      label: "Happy Customers",
      description: "Satisfied with our mobile service",
      color: "text-automotive-orange"
    },
    {
      icon: Clock,
      number: "15min",
      label: "Average Response",
      description: "From call to arrival time",
      color: "text-automotive-orange"
    },
    {
      icon: Star,
      number: "4.9/5",
      label: "Customer Rating",
      description: "Based on 2,500+ reviews",
      color: "text-automotive-orange"
    },
    {
      icon: Wrench,
      number: "50+",
      label: "Certified Technicians",
      description: "ASE certified professionals",
      color: "text-automotive-blue"
    },
    {
      icon: MapPin,
      number: "100mi",
      label: "Service Radius",
      description: "Covering metro area",
      color: "text-automotive-blue"
    }
  ];

  const certifications = [
    "ASE Certified Technicians",
    "EPA Certified Service",
    "OSHA Safety Compliant",
    "BBB A+ Rating",
    "ISO 9001:2015 Quality",
    "State Licensed & Insured"
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-4">
            Why Choose DiagOnWheels
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Trusted by Thousands of
            <span className="text-automotive-blue"> Vehicle Owners</span>
          </h2>
        </div>
        
        {/* Update stat cards to use consistent styling */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="border border-border/50 hover:border-automotive-blue/50 transition-all duration-300 hover:shadow-md">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className={`p-3 rounded-full ${stat.color === 'text-automotive-blue' ? 'bg-automotive-blue/10' : 'bg-automotive-orange/10'}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                  <div className={`text-4xl font-bold ${stat.color} mb-2`}>
                    {stat.number}
                  </div>
                  <h3 className="text-xl font-semibold text-automotive-dark mb-2">
                    {stat.label}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    {stat.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Certifications */}
        <div className="bg-gradient-to-r from-automotive-gray to-background rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-automotive-dark mb-4 flex items-center justify-center">
              <Shield className="w-8 h-8 text-automotive-blue mr-3" />
              Certifications & Credentials
            </h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We maintain the highest industry standards through continuous certification and training programs.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {certifications.map((cert, index) => (
              <div 
                key={index} 
                className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 flex items-center"
              >
                <div className="w-2 h-2 bg-automotive-blue rounded-full mr-3 flex-shrink-0"></div>
                <span className="text-automotive-dark font-medium">{cert}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Quality Guarantee */}
        <div className="mt-16 text-center">
          <div className="bg-automotive-blue rounded-2xl p-8 text-white max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold mb-4">Our Quality Guarantee</h3>
            <p className="text-primary-foreground text-lg mb-6 leading-relaxed">
              We stand behind our work with a 100% satisfaction guarantee. If you're not completely satisfied
              with our diagnostic service, we'll make it right or provide a full refund.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Badge className="bg-automotive-orange text-white px-4 py-2">
                30-Day Warranty
              </Badge>
              <Badge className="bg-automotive-orange text-white px-4 py-2">
                Money-Back Guarantee
              </Badge>
              <Badge className="bg-automotive-orange text-white px-4 py-2">
                Licensed & Insured
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AutomotiveStats;

