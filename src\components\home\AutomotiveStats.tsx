
"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Car, 
  Clock, 
  Star, 
  Shield, 
  Wrench, 
  Calendar,
  MapPin 
} from "lucide-react";

const AutomotiveStats = () => {
  const stats = [
    {
      icon: Users,
      number: "500+",
      label: "Happy Customers",
      description: "Satisfied vehicle owners across Nairobi",
      color: "text-automotive-blue"
    },
    {
      icon: Star,
      number: "4.9/5",
      label: "Customer Rating",
      description: "Based on verified customer reviews",
      color: "text-automotive-orange"
    },
    {
      icon: Clock,
      number: "< 30min",
      label: "Response Time",
      description: "Average arrival time for services",
      color: "text-automotive-blue"
    },
    {
      icon: Car,
      number: "1000+",
      label: "Vehicles Serviced",
      description: "Successfully diagnosed and serviced",
      color: "text-automotive-orange"
    },
    {
      icon: Wrench,
      number: "100%",
      label: "Certified Team",
      description: "Professional certified technicians",
      color: "text-automotive-blue"
    },
    {
      icon: MapPin,
      number: "Nairobi",
      label: "Service Area",
      description: "Covering greater Nairobi area",
      color: "text-automotive-orange"
    }
  ];

  const certifications = [
    "Certified Automotive Technicians",
    "Licensed Business Operations",
    "Comprehensive Insurance Coverage",
    "Quality Service Standards",
    "Professional Equipment Standards",
    "Customer Satisfaction Guarantee"
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-4">
            Why Choose DiagOnWheels
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Trusted by Thousands of
            <span className="text-automotive-blue"> Vehicle Owners</span>
          </h2>
        </div>
        
        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="border border-border/50 hover:border-automotive-blue/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <div className={`p-3 rounded-full ${stat.color === 'text-automotive-blue' ? 'bg-automotive-blue/10' : 'bg-automotive-orange/10'}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
                <div className={`text-3xl font-bold ${stat.color} mb-2`}>
                  {stat.number}
                </div>
                <h3 className="text-lg font-semibold text-automotive-dark mb-2">
                  {stat.label}
                </h3>
                <p className="text-muted-foreground text-sm">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Certifications */}
        <div className="bg-gradient-to-r from-automotive-gray to-background rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-automotive-dark mb-4 flex items-center justify-center">
              <Shield className="w-8 h-8 text-automotive-blue mr-3" />
              Certifications & Credentials
            </h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We maintain the highest industry standards through continuous certification and training programs.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {certifications.map((cert, index) => (
              <div 
                key={index} 
                className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 flex items-center"
              >
                <div className="w-2 h-2 bg-automotive-blue rounded-full mr-3 flex-shrink-0"></div>
                <span className="text-automotive-dark font-medium">{cert}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Quality Guarantee */}
        <div className="mt-16 text-center">
          <div className="bg-automotive-blue rounded-2xl p-8 text-white max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold mb-4">Our Quality Guarantee</h3>
            <p className="text-primary-foreground text-lg mb-6 leading-relaxed">
              We stand behind our work with a 100% satisfaction guarantee. If you're not completely satisfied
              with our diagnostic service, we'll make it right or provide a full refund.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Badge className="bg-automotive-orange text-white px-4 py-2">
                30-Day Warranty
              </Badge>
              <Badge className="bg-automotive-orange text-white px-4 py-2">
                Money-Back Guarantee
              </Badge>
              <Badge className="bg-automotive-orange text-white px-4 py-2">
                Licensed & Insured
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AutomotiveStats;

