
"use client";

import React from "react";
import AdminLayout from "@/components/layout/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Users, 
  Calendar, 
  FileText, 
  Settings,
  BarChart3,
  Shield,
  Bell,
  Database
} from "lucide-react";

const Admin = () => {
  const adminStats = [
    {
      title: "Total Bookings",
      value: "127",
      change: "+12%",
      icon: Calendar,
      color: "bg-automotive-blue"
    },
    {
      title: "Active Customers",
      value: "89",
      change: "+8%",
      icon: Users,
      color: "bg-automotive-orange"
    },
    {
      title: "Pending Reports",
      value: "23",
      change: "+3%",
      icon: FileText,
      color: "bg-automotive-orange"
    },
    {
      title: "System Health",
      value: "98%",
      change: "+2%",
      icon: BarChart3,
      color: "bg-automotive-blue"
    }
  ];

  const quickActions = [
    {
      title: "Manage Bookings",
      description: "View and manage customer appointments",
      icon: Calendar,
      action: "View Bookings"
    },
    {
      title: "Customer Database",
      description: "Access customer information and history",
      icon: Database,
      action: "Open Database"
    },
    {
      title: "System Settings",
      description: "Configure application settings",
      icon: Settings,
      action: "Settings"
    },
    {
      title: "Security Center",
      description: "Monitor security and access logs",
      icon: Shield,
      action: "Security"
    }
  ];

  const breadcrumbs = [
    { label: "Dashboard" }
  ];

  return (
    <AdminLayout breadcrumbs={breadcrumbs}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-automotive-dark">Admin Dashboard</h1>
            <p className="text-muted-foreground mt-2">Manage your DiagOnWheels operations</p>
          </div>
          <Badge variant="outline" className="border-primary text-primary">
            <div className="w-2 h-2 bg-primary rounded-full mr-2"></div>
            System Online
          </Badge>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {adminStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-automotive-dark">{stat.value}</p>
                  <p className="text-sm text-primary">{stat.change} from last month</p>
                </div>
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-automotive-dark mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action, index) => (
            <Card key={index} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-automotive-blue rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-automotive-orange transition-colors">
                  <action.icon className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-lg font-bold text-automotive-dark">
                  {action.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">{action.description}</p>
                <Button className="w-full bg-automotive-orange hover:bg-automotive-orange/90">
                  {action.action}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="w-5 h-5 mr-2 text-automotive-blue" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { time: "2 min ago", action: "New booking received from John Smith", type: "booking" },
              { time: "15 min ago", action: "Diagnostic report completed for Vehicle #4521", type: "report" },
              { time: "1 hour ago", action: "Payment processed for Invoice #INV-2024-0156", type: "payment" },
              { time: "2 hours ago", action: "New user registration: Sarah Johnson", type: "user" }
            ].map((activity, index) => (
              <div key={index} className="flex items-center space-x-4 p-3 bg-muted rounded-lg">
                <div className="w-2 h-2 bg-automotive-blue rounded-full"></div>
                <div className="flex-1">
                  <p className="text-automotive-dark">{activity.action}</p>
                  <p className="text-sm text-muted-foreground">{activity.time}</p>
                </div>
                <Badge variant="outline" className="text-xs">
                  {activity.type}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </AdminLayout>
  );
};

export default Admin;
