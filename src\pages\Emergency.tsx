import React from "react";
import { <PERSON> } from "react-router-dom";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  Clock,
  MapPin,
  Car,
  Wrench,
  ShieldCheck,
  Phone,
  AlertTriangle,
  ArrowRight,
  Zap,
  Users,
  Award,
  Star,
  CheckCircle,
  PhoneCall,
  Navigation,
  Tool,
  CreditCard,
  MessageCircle,
  Headphones,
  Timer,
  Shield
} from "lucide-react";
import WhatsAppButton from "@/components/home/<USER>";

const Emergency: React.FC = () => {
  const emergencyStats = [
    { number: "&lt; 30", label: "Minutes Response", icon: Timer },
    { number: "24/7", label: "Emergency Support", icon: Clock },
    { number: "98%", label: "Success Rate", icon: Award },
    { number: "500+", label: "Emergency Calls", icon: Phone },
  ];

  const emergencyFeatures = [
    {
      icon: Zap,
      title: "Instant Response",
      description: "Emergency dispatch activated immediately upon your call",
      highlight: "&lt; 2 minutes"
    },
    {
      icon: Navigation,
      title: "GPS Tracking",
      description: "Real-time technician location tracking and ETA updates",
      highlight: "Live Updates"
    },
    {
      icon: Tool,
      title: "Mobile Workshop",
      description: "Fully equipped emergency vehicles with diagnostic tools",
      highlight: "Complete Setup"
    },
    {
      icon: Shield,
      title: "Safety Priority",
      description: "Certified technicians trained in roadside safety protocols",
      highlight: "Certified Team"
    },
    {
      icon: Headphones,
      title: "24/7 Support",
      description: "Emergency hotline staffed around the clock every day",
      highlight: "Always Available"
    },
    {
      icon: CheckCircle,
      title: "Guaranteed Service",
      description: "60-minute response guarantee or diagnostic fee waived",
      highlight: "Money Back"
    }
  ];

  const emergencyProcess = [
    {
      step: 1,
      icon: PhoneCall,
      title: "Call Emergency Line",
      description: "Dial our 24/7 emergency hotline for immediate assistance",
      time: "Instant",
      color: "bg-red-500"
    },
    {
      step: 2,
      icon: MapPin,
      title: "Location Dispatch",
      description: "We locate the nearest certified technician to your position",
      time: "&lt; 2 mins",
      color: "bg-automotive-orange"
    },
    {
      step: 3,
      icon: Car,
      title: "Technician En Route",
      description: "Receive live updates with technician details and ETA",
      time: "&lt; 30 mins",
      color: "bg-automotive-blue"
    },
    {
      step: 4,
      icon: Wrench,
      title: "On-Site Service",
      description: "Professional diagnosis and emergency repairs performed",
      time: "30-60 mins",
      color: "bg-green-500"
    }
  ];

  const emergencyServices = [
    {
      icon: AlertTriangle,
      title: "Engine Failure",
      description: "Sudden engine failures, stalling, or won't start issues",
      price: "From KES 2,500",
      urgency: "Critical",
      responseTime: "&lt; 30 mins"
    },
    {
      icon: Zap,
      title: "Electrical Emergency",
      description: "Complete electrical failure, lights out, or charging issues",
      price: "From KES 2,000",
      urgency: "High",
      responseTime: "&lt; 30 mins"
    },
    {
      icon: Car,
      title: "Battery Dead",
      description: "Jump starts, battery testing, and emergency replacement",
      price: "From KES 1,500",
      urgency: "Medium",
      responseTime: "&lt; 25 mins"
    },
    {
      icon: AlertTriangle,
      title: "Overheating",
      description: "Engine overheating, coolant leaks, or temperature warnings",
      price: "From KES 2,000",
      urgency: "Critical",
      responseTime: "&lt; 20 mins"
    },
    {
      icon: Wrench,
      title: "Brake Problems",
      description: "Brake failure, warning lights, or safety-critical issues",
      price: "From KES 3,000",
      urgency: "Critical",
      responseTime: "&lt; 15 mins"
    },
    {
      icon: Car,
      title: "Transmission Issues",
      description: "Gear problems, fluid leaks, or transmission warnings",
      price: "From KES 2,500",
      urgency: "High",
      responseTime: "&lt; 30 mins"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const urgencyColors = {
    Critical: "bg-red-100 text-red-800 border-red-200",
    High: "bg-orange-100 text-orange-800 border-orange-200",
    Medium: "bg-yellow-100 text-yellow-800 border-yellow-200"
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main>
        {/* Emergency Hero Section */}
        <section className="relative bg-gradient-to-r from-red-600 via-automotive-orange to-automotive-dark text-white py-16 overflow-hidden">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            <motion.div
              className="absolute top-10 left-10 w-32 h-32 bg-white/5 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.1, 0.3]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-10 right-10 w-24 h-24 bg-white/5 rounded-full"
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
            />
          </div>

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="text-center max-w-5xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Badge variant="outline" className="border-white text-white mb-6 text-lg px-4 py-2">
                  🚨 24/7 Emergency Response
                </Badge>
              </motion.div>

              <motion.h1
                className="text-4xl lg:text-7xl font-bold mb-6"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Emergency
                <span className="text-yellow-300"> Auto Care</span>
              </motion.h1>

              <motion.p
                className="text-xl lg:text-2xl text-white/90 mb-8 leading-relaxed max-w-4xl mx-auto"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Stranded? Engine trouble? Don't panic. Our emergency response team is standing by
                24/7 to get you back on the road safely and quickly.
              </motion.p>

              {/* Emergency Contact - Most Prominent */}
              <motion.div
                className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8 border border-white/20"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{
                  opacity: 1,
                  scale: [1, 1.05, 1]
                }}
                transition={{
                  opacity: { duration: 0.8, delay: 0.6 },
                  scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                }}
              >
                <div className="flex flex-col items-center">
                  <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mb-4">
                    <Phone className="w-10 h-10 text-automotive-orange" />
                  </div>
                  <h2 className="text-2xl font-bold mb-2">Emergency Hotline</h2>
                  <div className="text-5xl lg:text-6xl font-bold text-yellow-300 mb-4 tracking-wider">
                    0727 795 520
                  </div>
                  <p className="text-white/80 mb-6 max-w-md">
                    Available 24/7 • Average response time under 30 minutes
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 justify-center"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                <Button
                  size="lg"
                  className="bg-white text-automotive-orange hover:bg-white/90 text-xl px-8 py-6 font-bold shadow-2xl"
                  asChild
                >
                  <a href="tel:+254727795520">
                    <Phone className="w-6 h-6 mr-3" />
                    CALL NOW - EMERGENCY
                  </a>
                </Button>
                <Button
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-automotive-dark text-xl px-8 py-6"
                >
                  <Link to="/booking">
                    <MessageCircle className="w-6 h-6 mr-3" />
                    Request Callback
                  </Link>
                </Button>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Emergency Stats */}
        <section className="py-12 bg-white border-b">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-8"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {emergencyStats.map((stat, index) => (
                <motion.div key={index} className="text-center group" variants={itemVariants}>
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-200 transition-colors">
                      <stat.icon className="w-8 h-8 text-red-600" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-automotive-dark mb-2">{stat.number}</div>
                  <div className="text-muted-foreground font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Emergency Process */}
        <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge variant="outline" className="border-automotive-blue text-automotive-blue mb-4">
                Emergency Process
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold text-automotive-dark mb-4">
                How Our Emergency
                <span className="text-automotive-orange"> Response Works</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                When you call our emergency line, here's exactly what happens to get you help fast.
              </p>
            </div>

            <div className="max-w-5xl mx-auto">
              {emergencyProcess.map((step, index) => (
                <motion.div
                  key={step.step}
                  className="relative mb-12 last:mb-0"
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                >
                  {/* Connector Line */}
                  {index < emergencyProcess.length - 1 && (
                    <div className="hidden lg:block absolute left-1/2 top-24 w-0.5 h-20 bg-gradient-to-b from-automotive-orange to-automotive-blue transform -translate-x-1/2 z-0"></div>
                  )}

                  <div className={`flex flex-col lg:flex-row items-center gap-8 ${
                    index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                  }`}>
                    {/* Step Content */}
                    <div className="flex-1 lg:max-w-md">
                      <Card className="bg-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                        <CardHeader className="pb-4">
                          <div className="flex items-center gap-4 mb-4">
                            <div className={`w-12 h-12 ${step.color} rounded-xl flex items-center justify-center`}>
                              <step.icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-8 h-8 ${step.color} rounded-full flex items-center justify-center`}>
                                <span className="text-white font-bold text-sm">{step.step}</span>
                              </div>
                              <Badge variant="outline" className="text-xs font-medium">
                                {step.time}
                              </Badge>
                            </div>
                          </div>
                          <CardTitle className="text-2xl font-bold text-automotive-dark">
                            {step.title}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-gray-600 leading-relaxed text-lg">
                            {step.description}
                          </p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Step Visual */}
                    <div className="flex-1 flex justify-center">
                      <div className="relative">
                        <motion.div
                          className={`w-32 h-32 ${step.color} rounded-full flex items-center justify-center shadow-2xl`}
                          whileHover={{ scale: 1.05 }}
                          transition={{ duration: 0.2 }}
                        >
                          <step.icon className="w-16 h-16 text-white" />
                        </motion.div>
                        <div className="absolute -top-2 -right-2 w-10 h-10 bg-white rounded-full flex items-center justify-center border-4 border-automotive-orange shadow-lg">
                          <span className="text-automotive-orange font-bold">{step.step}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Emergency Features */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-4">
                Emergency Capabilities
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold text-automotive-dark mb-4">
                Why Choose Our
                <span className="text-automotive-orange"> Emergency Service</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Professional emergency automotive care with the speed and reliability you need in critical situations.
              </p>
            </div>

            <motion.div
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
            >
              {emergencyFeatures.map((feature, index) => (
                <motion.div key={index} variants={itemVariants}>
                  <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white group">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="bg-automotive-orange/10 p-3 rounded-xl flex-shrink-0 group-hover:bg-automotive-orange/20 transition-colors">
                          <feature.icon className="w-6 h-6 text-automotive-orange" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="text-lg font-bold text-automotive-dark group-hover:text-automotive-blue transition-colors">
                              {feature.title}
                            </h3>
                            <Badge variant="outline" className="text-xs bg-automotive-blue/10 text-automotive-blue border-automotive-blue/20">
                              {feature.highlight}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground leading-relaxed">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Emergency Services */}
        <section className="py-20 bg-automotive-gray/5">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge variant="outline" className="border-red-500 text-red-600 mb-4">
                Emergency Services
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold text-automotive-dark mb-4">
                Critical Emergency
                <span className="text-automotive-orange"> Situations We Handle</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our emergency response team is equipped to handle the most critical automotive situations with priority response times.
              </p>
            </div>

            <motion.div
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {emergencyServices.map((service, index) => (
                <motion.div key={index} variants={itemVariants}>
                  <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white group">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center group-hover:bg-red-200 transition-colors">
                          <service.icon className="w-6 h-6 text-red-600" />
                        </div>
                        <div className="flex flex-col gap-1">
                          <Badge
                            variant="outline"
                            className={`text-xs font-medium w-fit ${urgencyColors[service.urgency as keyof typeof urgencyColors]}`}
                          >
                            {service.urgency} Priority
                          </Badge>
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <Timer className="w-3 h-3" />
                            {service.responseTime}
                          </div>
                        </div>
                      </div>
                      <CardTitle className="text-xl font-bold text-automotive-dark group-hover:text-automotive-blue transition-colors">
                        {service.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        {service.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="text-automotive-orange font-bold text-lg">
                          {service.price}
                        </div>
                        <Button size="sm" className="bg-red-600 hover:bg-red-700 text-white">
                          Call Now
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>

            {/* Emergency Contact Banner */}
            <motion.div
              className="bg-gradient-to-r from-red-600 via-automotive-orange to-automotive-dark rounded-2xl p-8 text-center text-white shadow-2xl overflow-hidden relative"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              {/* Animated Pulse Effects */}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  className="w-40 h-40 rounded-full bg-white/10"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.3, 0.1, 0.3]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <motion.div
                  className="absolute w-60 h-60 rounded-full bg-white/5"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.2, 0.05, 0.2]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5
                  }}
                />
              </div>

              <div className="relative z-10">
                <div className="flex items-center justify-center mb-6">
                  <motion.div
                    className="w-20 h-20 bg-white rounded-full flex items-center justify-center"
                    animate={{
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Phone className="w-10 h-10 text-automotive-orange" />
                  </motion.div>
                </div>
                <h3 className="text-4xl font-bold mb-4">🚨 EMERGENCY HOTLINE 🚨</h3>
                <div className="text-6xl lg:text-7xl font-bold mb-6 text-yellow-300 tracking-wider">
                  0727 795 520
                </div>
                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
                  Don't wait when you're stranded! Our emergency dispatch team is standing by 24/7
                  to send immediate help to your location. Average response time under 30 minutes.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-white text-red-600 hover:bg-white/90 text-xl px-8 py-6 font-bold shadow-2xl" asChild>
                    <a href="tel:+254727795520">
                      <Phone className="w-6 h-6 mr-3" />
                      CALL EMERGENCY NOW
                    </a>
                  </Button>
                  <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 text-xl px-8 py-6">
                    <MapPin className="w-6 h-6 mr-3" />
                    Share Location
                  </Button>
                </div>

                <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-6 text-white/80">
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    <span className="font-medium">24/7 Available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Timer className="w-5 h-5" />
                    <span className="font-medium">&lt; 30 Min Response</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    <span className="font-medium">Certified Technicians</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Service Guarantee & Trust Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              {/* Service Guarantee */}
              <motion.div
                className="bg-gradient-to-r from-automotive-blue to-automotive-dark rounded-2xl p-8 text-white text-center mb-12"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center justify-center mb-6">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                </div>
                <h3 className="text-3xl font-bold mb-4">Emergency Service Guarantee</h3>
                <p className="text-xl text-white/90 mb-6 leading-relaxed">
                  We guarantee a certified technician will arrive within 60 minutes of your emergency call,
                  or your diagnostic fee is completely waived. Your safety and satisfaction are our top priorities.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-automotive-orange hover:bg-automotive-orange/90 text-white">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    View Service Area
                  </Button>
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                    <Star className="w-5 h-5 mr-2" />
                    Read Reviews
                  </Button>
                </div>
              </motion.div>

              {/* Trust Indicators */}
              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <h4 className="text-lg font-bold text-automotive-dark mb-2">Licensed & Insured</h4>
                  <p className="text-muted-foreground">All technicians are certified and fully insured for your protection</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="w-8 h-8 text-blue-600" />
                  </div>
                  <h4 className="text-lg font-bold text-automotive-dark mb-2">5-Star Rated</h4>
                  <p className="text-muted-foreground">Consistently rated 5 stars by customers for emergency response</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-orange-600" />
                  </div>
                  <h4 className="text-lg font-bold text-automotive-dark mb-2">500+ Emergencies</h4>
                  <p className="text-muted-foreground">Successfully handled over 500 emergency calls this year</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default Emergency;


