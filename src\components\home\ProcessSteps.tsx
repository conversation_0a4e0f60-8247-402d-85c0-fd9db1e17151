import React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>eader, CardContent, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { 
  PhoneCall, 
  Calendar, 
  Car, 
  Wrench, 
  ClipboardCheck, 
  CreditCard,
  ArrowRight
} from "lucide-react";

const ProcessSteps = () => {
  const steps = [
    {
      number: "1",
      icon: PhoneCall,
      title: "Request Service",
      description: "Call us or book online with your vehicle details and location",
      color: "bg-automotive-blue"
    },
    {
      number: "2",
      icon: Calendar,
      title: "Schedule Visit",
      description: "Choose a convenient time for our technician to visit you",
      color: "bg-automotive-orange"
    },
    {
      number: "3",
      icon: Car,
      title: "We Come to You",
      description: "Our mobile unit arrives at your location with all equipment",
      color: "bg-automotive-blue"
    },
    {
      number: "4",
      icon: Wrench,
      title: "Diagnostic Service",
      description: "We perform comprehensive diagnostics on your vehicle",
      color: "bg-automotive-blue"
    },
    {
      number: "5",
      icon: <PERSON>lip<PERSON><PERSON><PERSON><PERSON>,
      title: "Detailed Report",
      description: "Receive a comprehensive report with findings and recommendations",
      color: "bg-automotive-blue"
    },
    {
      number: "6",
      icon: CreditCard,
      title: "Payment Process",
      description: "Pay securely online or in person with our mobile payment options",
      color: "bg-automotive-orange"
    },
    {
      number: "7",
      icon: ArrowRight,
      title: "Service Completion",
      description: "Your vehicle is ready! We'll follow up to discuss next steps",
      color: "bg-automotive-blue"
    }
  ];

  return (
    <section className="py-16 bg-automotive-gray/5">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12 animate-fade-in">
          <Badge variant="outline" className="border-automotive-blue text-automotive-blue mb-3">
            How It Works
          </Badge>
          <h2 className="text-3xl font-bold text-automotive-dark mb-4">
            Simple 7-Step
            <span className="text-automotive-orange"> Process</span>
          </h2>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto">
            Getting professional automotive diagnostics has never been easier.
            Our streamlined process ensures quick, efficient service at your convenience.
          </p>
        </div>

        {/* Process Steps */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {steps.map((step, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-md transition-all duration-300 hover:-translate-y-1 border border-border relative overflow-hidden"
            >
              {/* Step Number */}
              <div className="absolute top-4 right-4">
                <div className={`w-8 h-8 ${step.color} rounded-full flex items-center justify-center`}>
                  <span className="text-white font-bold text-sm">{step.number}</span>
                </div>
              </div>

              <CardHeader className="pb-2 pt-6">
                <div className={`w-12 h-12 ${step.color} rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300`}>
                  <step.icon className="w-6 h-6 text-white" />
                </div>
                
                <CardTitle className="text-lg font-bold text-automotive-dark group-hover:text-automotive-blue transition-colors">
                  {step.title}
                </CardTitle>
              </CardHeader>

              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {step.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-automotive-blue to-automotive-dark rounded-xl p-6 text-white">
            <h3 className="text-xl font-bold mb-3">Ready to Get Started?</h3>
            <p className="text-primary-foreground mb-5 max-w-xl mx-auto text-sm">
              Experience the convenience of professional mobile automotive diagnostics.
              Book your service today and let us come to you.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link to="/booking">
                <Button className="bg-automotive-orange hover:bg-automotive-orange/90 text-white px-6 py-2 rounded-md font-medium transition-colors duration-300 hover:shadow-lg">
                  Book Now
                </Button>
              </Link>
              <Link to="/how-it-works">
                <Button variant="outline" className="border border-white/20 bg-transparent hover:bg-white/10 text-white px-6 py-2 rounded-md font-medium transition-colors duration-300">
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProcessSteps;

