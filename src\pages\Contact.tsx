import React, { useState, useRef, useEffect } from "react";
import emailjs from "@emailjs/browser";
import { motion } from "framer-motion";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Phone, 
  Mail, 
  MapPin, 
  Send, 
  MessageSquare, 
  Globe, 
  Clock, 
  Facebook, 
  Twitter, 
  Instagram, 
  CheckCircle, 
  AlertCircle 
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { z } from "zod";
import WhatsAppButton from "@/components/home/<USER>";

const Contact = () => {
  const { toast } = useToast();
  const form = useRef<HTMLFormElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    full_name: "",
    email_address: "",
    phone_number: "",
    subject: "",
    message: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formTouched, setFormTouched] = useState(false);

  const businessInfo = [
    {
      icon: <MapPin className="w-5 h-5 text-automotive-orange" />,
      label: "Address",
      value: "Nairobi, Kenya (Mobile Service)",
    },
    {
      icon: <Phone className="w-5 h-5 text-automotive-orange" />,
      label: "Phone",
      value: "0727 795 520",
      href: "tel:+254727795520",
    },
    {
      icon: <Mail className="w-5 h-5 text-automotive-orange" />,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
    },
    {
      icon: <Globe className="w-5 h-5 text-automotive-orange" />,
      label: "Website",
      value: "www.diagonwheels.com",
      href: "https://www.diagonwheels.com",
    },
    {
      icon: <Clock className="w-5 h-5 text-automotive-orange" />,
      label: "Working Hours",
      value: "8:00 AM – 6:00 PM, Mon–Sat",
    },
  ];

  const socialLinks = [
    { icon: Facebook, label: "Facebook", href: "https://facebook.com/diagonwheels" },
    { icon: Twitter, label: "Twitter", href: "https://twitter.com/diagonwheels" },
    { icon: Instagram, label: "Instagram", href: "https://instagram.com/diagonwheels" },
    { icon: MessageSquare, label: "WhatsApp", href: "https://wa.me/254727795520" }
  ];

  // Form validation schema
  const formSchema = z.object({
    full_name: z.string().min(2, "Name must be at least 2 characters"),
    email_address: z.string().email("Please enter a valid email address"),
    phone_number: z.string().optional(),
    subject: z.string().min(3, "Subject must be at least 3 characters"),
    message: z.string().min(10, "Message must be at least 10 characters")
  });

  const validateForm = () => {
    try {
      formSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0].toString()] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setFormTouched(true);
  };

  useEffect(() => {
    if (formTouched) {
      validateForm();
    }
  }, [formData, formTouched]);

  const sendEmail = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Form Validation Error",
        description: "Please check the form for errors and try again.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);

    if (form.current) {
      emailjs
        .sendForm(
          "service_ykolb3k",
          "template_dmxnifa",
          form.current,
          "44Uiv1RjBhxgg1wZa"
        )
        .then(
          (result) => {
            console.log("SUCCESS!", result.text);
            toast({
              title: "Message Sent Successfully!",
              description: "Thank you for contacting us. We'll get back to you shortly.",
              variant: "default",
              className: "bg-automotive-blue text-white",
              action: (
                <div className="h-8 w-8 bg-white rounded-full flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-automotive-blue" />
                </div>
              ),
            });
            
            // Reset form
            setFormData({
              full_name: "",
              email_address: "",
              phone_number: "",
              subject: "",
              message: ""
            });
            setFormTouched(false);
            form.current?.reset();
          },
          (error) => {
            console.error("FAILED...", error.text);
            toast({
              title: "Message Could Not Be Sent",
              description: "There was a problem sending your message. Please try again or contact us directly.",
              variant: "destructive",
              action: (
                <div className="h-8 w-8 bg-white rounded-full flex items-center justify-center">
                  <AlertCircle className="h-5 w-5 text-destructive" />
                </div>
              ),
            });
          }
        )
        .finally(() => {
          setIsSubmitting(false);
        });
    }
  };
  
  const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  const staggerChildren = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariant = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 font-inter">
      <Header />
      <main className="py-12 sm:py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12 sm:mb-16"
            initial="hidden"
            animate="visible"
            variants={sectionVariants}
          >
            <div className="inline-flex items-center justify-center bg-automotive-blue/10 rounded-full px-4 py-1 mb-4">
              <Phone className="w-4 h-4 text-automotive-blue mr-2" />
              <span className="text-sm font-medium text-automotive-blue">Get In Touch</span>
            </div>
            <h1 className="text-4xl sm:text-5xl font-bold text-automotive-dark mb-3 sm:mb-4">
              Contact <span className="text-automotive-orange">DiagOnWheels</span>
            </h1>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto">
              Have questions about our mobile diagnostic services? We're just a message away.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-12 gap-8 sm:gap-12 items-start">
            {/* Contact Form Card */}
            <motion.div 
              className="lg:col-span-7"
              initial="hidden"
              animate="visible"
              variants={{ ...sectionVariants, visible: { ...sectionVariants.visible, transition: { delay: 0.2, duration: 0.6 }}}}
            >
              <Card className="shadow-xl border-border rounded-xl overflow-hidden">
                <CardHeader className="bg-automotive-blue border-b border-border p-6">
                  <CardTitle className="text-2xl font-semibold text-white flex items-center">
                    <Send className="w-6 h-6 mr-3 text-automotive-orange" />
                    Send Us a Message
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 sm:p-8">
                  <form ref={form} onSubmit={sendEmail} className="space-y-5">
                    <div className="grid sm:grid-cols-2 gap-5">
                      <div>
                        <Label htmlFor="full_name" className="font-medium text-foreground">
                          Full Name <span className="text-red-500">*</span>
                        </Label>
                        <Input 
                          id="full_name" 
                          name="full_name" 
                          type="text" 
                          placeholder="e.g., Jane Doe" 
                          required 
                          className={`mt-2 ${errors.full_name ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'focus:ring-automotive-blue focus:border-automotive-blue'}`}
                          value={formData.full_name}
                          onChange={handleInputChange}
                        />
                        {errors.full_name && (
                          <p className="text-red-500 text-xs mt-1">{errors.full_name}</p>
                        )}
                      </div>
                      <div>
                        <Label htmlFor="email_address" className="font-medium text-foreground">
                          Email Address <span className="text-red-500">*</span>
                        </Label>
                        <Input 
                          id="email_address" 
                          name="email_address" 
                          type="email" 
                          placeholder="<EMAIL>" 
                          required 
                          className={`mt-2 ${errors.email_address ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'focus:ring-automotive-blue focus:border-automotive-blue'}`}
                          value={formData.email_address}
                          onChange={handleInputChange}
                        />
                        {errors.email_address && (
                          <p className="text-red-500 text-xs mt-1">{errors.email_address}</p>
                        )}
                      </div>
                    </div>
                    <div className="grid sm:grid-cols-2 gap-5">
                      <div>
                        <Label htmlFor="phone_number" className="font-medium text-foreground">
                          Phone Number <span className="text-sm text-muted-foreground">(Optional)</span>
                        </Label>
                        <Input 
                          id="phone_number" 
                          name="phone_number" 
                          type="tel" 
                          placeholder="+254 7XX XXX XXX" 
                          className="mt-2 focus:ring-automotive-blue focus:border-automotive-blue"
                          value={formData.phone_number}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div>
                        <Label htmlFor="subject" className="font-medium text-foreground">
                          Subject <span className="text-red-500">*</span>
                        </Label>
                        <Input 
                          id="subject" 
                          name="subject" 
                          type="text" 
                          placeholder="e.g., Inquiry about Engine Diagnostics" 
                          required 
                          className={`mt-2 ${errors.subject ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'focus:ring-automotive-blue focus:border-automotive-blue'}`}
                          value={formData.subject}
                          onChange={handleInputChange}
                        />
                        {errors.subject && (
                          <p className="text-red-500 text-xs mt-1">{errors.subject}</p>
                        )}
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="message" className="font-medium text-foreground">
                        Message <span className="text-red-500">*</span>
                      </Label>
                      <Textarea
                        id="message"
                        name="message"
                        placeholder="Please describe your needs or questions here..."
                        required
                        rows={5}
                        className={`mt-2 ${errors.message ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'focus:ring-automotive-blue focus:border-automotive-blue'}`}
                        value={formData.message}
                        onChange={handleInputChange}
                      />
                      {errors.message && (
                        <p className="text-red-500 text-xs mt-1">{errors.message}</p>
                      )}
                    </div>
                    <div className="pt-2">
                      <Button
                        type="submit"
                        size="lg"
                        className="w-full bg-automotive-orange hover:bg-automotive-orange/90 text-white font-semibold py-3 text-base transition-all duration-300 ease-in-out transform hover:scale-105 disabled:opacity-70 rounded-full"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                            Sending...
                          </>
                        ) : (
                          <>
                            Send Message
                            <Send className="w-4 h-4 ml-2" />
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </motion.div>

            {/* Business Info & Map */}
            <motion.div 
              className="lg:col-span-5 space-y-8"
              initial="hidden"
              animate="visible"
              variants={{ ...sectionVariants, visible: { ...sectionVariants.visible, transition: { delay: 0.4, duration: 0.6 }}}}
            >
              <Card className="shadow-xl border-border rounded-xl overflow-hidden">
                <CardHeader className="bg-automotive-blue border-b border-border p-6">
                  <CardTitle className="text-2xl font-semibold text-white flex items-center">
                    <MapPin className="w-6 h-6 mr-3 text-automotive-orange" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <motion.div 
                    className="space-y-5"
                    variants={staggerChildren}
                    initial="hidden"
                    animate="visible"
                  >
                    {businessInfo.map((item) => (
                      <motion.div key={item.label} variants={itemVariant} className="flex items-start space-x-3 group">
                        <div className="flex-shrink-0 mt-1 w-10 h-10 bg-automotive-blue/10 rounded-full flex items-center justify-center group-hover:bg-automotive-blue/20 transition-colors">
                          {item.icon}
                        </div>
                        <div>
                          <h3 className="text-base font-semibold text-automotive-dark">
                            {item.label}
                          </h3>
                          {item.href ? (
                            <a 
                              href={item.href} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-muted-foreground hover:text-automotive-orange transition-colors break-all text-sm group-hover:underline"
                            >
                              {item.value}
                            </a>
                          ) : (
                            <p className="text-muted-foreground break-all text-sm">{item.value}</p>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>

                  <div className="mt-8 pt-6 border-t border-border">
                    <h3 className="text-base font-semibold text-automotive-dark mb-4">Connect With Us</h3>
                    <div className="flex space-x-3">
                      {socialLinks.map((social) => (
                        <a
                          key={social.label}
                          href={social.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-10 h-10 bg-automotive-blue/10 rounded-full flex items-center justify-center text-automotive-blue hover:bg-automotive-blue hover:text-white transition-all duration-300"
                          aria-label={`Connect with us on ${social.label}`}
                        >
                          <social.icon className="w-5 h-5" />
                        </a>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-xl border-border rounded-xl overflow-hidden">
                <CardHeader className="bg-automotive-blue border-b border-border p-6">
                  <CardTitle className="text-2xl font-semibold text-white flex items-center">
                    <Globe className="w-6 h-6 mr-3 text-automotive-orange" />
                    Our Service Area
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="aspect-w-16 aspect-h-9 rounded-b-xl overflow-hidden">
                    <iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63820.29770774605!2d36.82221577431642!3d-1.3028602499999998!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f1172d84d49a7%3A0xf7cf0254b297924c!2sNairobi%2C%20Kenya!5e0!3m2!1sen!2sus!4v1678886500000!5m2!1sen!2sus"
                      width="100%"
                      height="300"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title="DiagOnWheels Service Area"
                    ></iframe>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Additional Contact Info Section */}
          <motion.div
            className="mt-16 text-center"
            initial="hidden"
            animate="visible"
            variants={{ ...sectionVariants, visible: { ...sectionVariants.visible, transition: { delay: 0.6, duration: 0.6 }}}}
          >
            <div className="inline-flex justify-center items-center w-16 h-16 bg-automotive-blue/10 rounded-full mb-4">
              <Phone className="w-8 h-8 text-automotive-blue" />
            </div>
            <h3 className="text-2xl font-bold text-automotive-dark mb-4">Need Immediate Assistance?</h3>
            <p className="text-muted-foreground mb-6 max-w-xl mx-auto">
              For urgent inquiries or emergency services, call our hotline directly or use our WhatsApp service for instant responses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-automotive-orange hover:bg-automotive-orange/90">
                <a href="tel:+254727795520">
                  <Phone className="mr-2 w-5 h-5" />
                  Call Now: 0727 795 520
                </a>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-automotive-blue text-automotive-blue hover:bg-automotive-blue/5">
                <a href="https://wa.me/254727795520" target="_blank" rel="noopener noreferrer">
                  <MessageSquare className="mr-2 w-5 h-5" />
                  WhatsApp Us
                </a>
              </Button>
            </div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default Contact;
