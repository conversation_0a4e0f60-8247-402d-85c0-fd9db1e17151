
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 208 17% 24%;

    --card: 0 0% 100%;
    --card-foreground: 208 17% 24%;

    --popover: 0 0% 100%;
    --popover-foreground: 208 17% 24%;

    --primary: 210 100% 20%;
    --primary-foreground: 0 0% 100%;

    --secondary: 208 17% 24%;
    --secondary-foreground: 0 0% 100%;

    --muted: 208 17% 90%;
    --muted-foreground: 208 17% 45%;

    --accent: 24 100% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 24 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 208 17% 85%;
    --input: 208 17% 85%;
    --ring: 210 100% 20%;

    --radius: 0.5rem;

    /* Custom automotive brand colors */
    --automotive-blue: 210 100% 20%;
    --automotive-orange: 24 100% 50%;
    --automotive-dark: 210 100% 10%;
    --automotive-gray: 208 17% 24%;
    --automotive-success: 210 100% 20%;
    --automotive-warning: 24 100% 50%;
  }

  .dark {
    --background: 210 100% 10%;
    --foreground: 0 0% 100%;

    --card: 210 100% 10%;
    --card-foreground: 0 0% 100%;

    --popover: 210 100% 10%;
    --popover-foreground: 0 0% 100%;

    --primary: 210 100% 20%;
    --primary-foreground: 0 0% 100%;

    --secondary: 208 17% 24%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 100% 15%;
    --muted-foreground: 0 0% 80%;

    --accent: 24 100% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 24 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 100% 15%;
    --input: 210 100% 15%;
    --ring: 210 100% 20%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom automotive brand utility classes */
@layer utilities {
  .text-automotive-blue {
    color: hsl(var(--automotive-blue));
  }
  
  .text-automotive-orange {
    color: hsl(var(--automotive-orange));
  }
  
  .text-automotive-dark {
    color: hsl(var(--automotive-dark));
  }
  
  .bg-automotive-blue {
    background-color: hsl(var(--automotive-blue));
  }
  
  .bg-automotive-orange {
    background-color: hsl(var(--automotive-orange));
  }
  
  .bg-automotive-dark {
    background-color: hsl(var(--automotive-dark));
  }
  
  .bg-automotive-gray {
    background-color: hsl(var(--automotive-gray));
  }
  
  .border-automotive-blue {
    border-color: hsl(var(--automotive-blue));
  }
  
  .border-automotive-orange {
    border-color: hsl(var(--automotive-orange));
  }
}

/* Add consistent transition classes */
@layer utilities {
  .transition-brand {
    @apply transition-all duration-300;
  }
  
  .hover-lift {
    @apply hover:-translate-y-1 transition-transform duration-300;
  }
  
  .hover-glow-blue {
    @apply hover:shadow-[0_0_15px_rgba(0,76,153,0.5)] transition-shadow duration-300;
  }
  
  .hover-glow-orange {
    @apply hover:shadow-[0_0_15px_rgba(255,128,0,0.5)] transition-shadow duration-300;
  }
  
  /* Ensure proper contrast for accessibility */
  .text-on-dark {
    @apply text-white font-medium;
  }
  
  .text-on-light {
    @apply text-automotive-dark font-medium;
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0) translateX(-50%);
  }
  50% {
    transform: translateY(-8px) translateX(-50%);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-float-slow {
  animation: float-slow 6s infinite ease-in-out;
}

.animate-pulse-slow {
  animation: pulse-slow 4s infinite ease-in-out;
}

.animate-bounce-slow {
  animation: bounce-slow 3s infinite ease-in-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fade-in-right 0.8s ease-out forwards;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}



