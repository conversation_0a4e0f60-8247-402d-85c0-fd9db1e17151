import React, { useState, useMemo } from "react";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
  HelpCircle,
  ArrowRight,
  Search,
  Phone,
  MessageCircle,
  Clock,
  MapPin,
  Shield,
  CheckCircle,
  AlertTriangle,
  Car,
  Wrench,
  CreditCard,
  Calendar
} from "lucide-react";
import WhatsAppButton from "@/components/home/<USER>";

const FAQ = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");

  const faqCategories = [
    {
      id: "services",
      name: "Services & Coverage",
      icon: Car,
      color: "text-automotive-blue"
    },
    {
      id: "pricing",
      name: "Pricing & Payment",
      icon: CreditCard,
      color: "text-automotive-orange"
    },
    {
      id: "booking",
      name: "Booking & Scheduling",
      icon: Calendar,
      color: "text-automotive-blue"
    },
    {
      id: "emergency",
      name: "Emergency Services",
      icon: AlertTriangle,
      color: "text-red-600"
    },
    {
      id: "technical",
      name: "Technical & Equipment",
      icon: Wrench,
      color: "text-automotive-blue"
    },
    {
      id: "warranty",
      name: "Warranty & Guarantees",
      icon: Shield,
      color: "text-green-600"
    }
  ];

  const faqs = [
    // Services & Coverage
    {
      category: "services",
      question: "What areas in Nairobi do you cover for mobile diagnostic services?",
      answer: "We provide comprehensive mobile diagnostic services throughout Nairobi and surrounding areas including Westlands, Karen, Lavington, Kileleshwa, CBD, Upperhill, Langata, Kasarani, Embakasi, and many more. Our service area extends to Kiambu, Thika Road corridor, and parts of Machakos. Average response time is 15-45 minutes depending on your location. Contact us to confirm service availability in your specific area.",
      tags: ["coverage", "nairobi", "areas", "location"]
    },
    {
      category: "services",
      question: "What types of vehicles do you service?",
      answer: "We service all types of vehicles including passenger cars (Toyota, Nissan, Honda, Mazda, Subaru, Volkswagen, Mercedes-Benz, BMW), SUVs and pickup trucks (Land Cruiser, Hilux, Pajero, X-Trail), and commercial vehicles (Hiace, Probox, Isuzu trucks). Our technicians are experienced with both petrol and diesel engines, manual and automatic transmissions, and modern computerized systems.",
      tags: ["vehicles", "cars", "toyota", "nissan", "suv", "commercial"]
    },
    {
      category: "services",
      question: "Do you offer on-the-spot repairs after diagnosis?",
      answer: "Yes! Many common issues can be resolved on-site including battery replacement, alternator testing, starter motor issues, minor electrical repairs, fluid top-ups, belt replacements, and basic maintenance. For complex repairs requiring specialized parts or workshop facilities, we provide detailed diagnosis and recommend trusted repair shops with transparent pricing.",
      tags: ["repairs", "on-site", "battery", "electrical", "maintenance"]
    },
    {
      category: "services",
      question: "What diagnostic equipment do you bring?",
      answer: "Our mobile units are fully equipped with professional diagnostic tools including advanced OBD-II scanners, multimeters, battery load testers, compression testers, oscilloscopes, and specialized diagnostic software for various vehicle makes. We bring everything needed to perform comprehensive vehicle diagnostics at your location.",
      tags: ["equipment", "tools", "obd", "scanner", "professional"]
    },

    // Pricing & Payment
    {
      category: "pricing",
      question: "How much do your diagnostic services cost?",
      answer: "Our transparent pricing starts from KES 1,500 for basic services like battery testing, KES 2,000-2,500 for engine diagnostics and OBD scanning, KES 2,500-3,000 for electrical system diagnostics, and KES 3,000-3,500 for comprehensive pre-purchase inspections. Emergency services may have additional charges for after-hours calls. We provide upfront pricing with no hidden fees.",
      tags: ["cost", "price", "fees", "kes", "diagnostic"]
    },
    {
      category: "pricing",
      question: "What payment methods do you accept?",
      answer: "We accept multiple convenient payment methods including cash, M-Pesa (Paybill and Till numbers), bank transfers, Visa/Mastercard through our mobile payment system, and corporate invoicing for fleet customers. Payment is due upon completion of service, and we provide detailed invoices for all transactions.",
      tags: ["payment", "mpesa", "cash", "card", "invoice"]
    },
    {
      category: "pricing",
      question: "Are there any additional charges I should know about?",
      answer: "Our pricing is transparent with no hidden fees. Additional charges may apply for: emergency services outside business hours (+KES 500), services beyond 30km from Nairobi CBD (+KES 200 per additional 10km), and if specialized diagnostic equipment is required for luxury vehicles. All additional charges are communicated upfront.",
      tags: ["charges", "fees", "emergency", "distance", "luxury"]
    },

    // Booking & Scheduling
    {
      category: "booking",
      question: "How quickly can a technician reach my location?",
      answer: "Our average response times are: 15-30 minutes within central Nairobi (CBD, Westlands, Upperhill), 20-40 minutes for residential areas (Karen, Lavington, Kileleshwa), and 30-50 minutes for outer areas (Kasarani, Embakasi, Thika Road). For emergencies, we guarantee arrival within 60 minutes or your diagnostic fee is waived.",
      tags: ["response", "time", "quick", "emergency", "arrival"]
    },
    {
      category: "booking",
      question: "Can I schedule a service in advance?",
      answer: "Absolutely! You can book our services in advance through our online booking system, WhatsApp, or by calling us. We offer flexible scheduling including same-day appointments, weekend services, and you can choose convenient time slots that work with your schedule. Advanced bookings get priority scheduling.",
      tags: ["schedule", "advance", "booking", "appointment", "weekend"]
    },
    {
      category: "booking",
      question: "Do I need to be present during the diagnostic service?",
      answer: "While not always required, we recommend being present so our technician can explain findings directly and answer any questions. If you can't be present, ensure vehicle access and provide a contact number. We'll send you a detailed report with photos and recommendations via WhatsApp or email.",
      tags: ["present", "owner", "access", "report", "contact"]
    },
    {
      category: "booking",
      question: "How long does a typical diagnostic service take?",
      answer: "Service duration varies by complexity: Basic battery/alternator testing (15-30 minutes), Standard engine diagnostics (30-60 minutes), Comprehensive electrical diagnostics (45-90 minutes), Pre-purchase inspections (60-120 minutes). We'll provide an estimated timeframe when you book and keep you updated throughout the service.",
      tags: ["duration", "time", "how long", "minutes", "inspection"]
    },

    // Emergency Services
    {
      category: "emergency",
      question: "Do you provide 24/7 emergency services?",
      answer: "Yes! Our emergency response team is available 24/7 for critical situations like engine failure, electrical problems, or when you're stranded. Call our emergency hotline at 0727 795 520 for immediate assistance. Emergency services have priority response with guaranteed arrival within 60 minutes in our service area.",
      tags: ["emergency", "24/7", "hotline", "stranded", "critical"]
    },
    {
      category: "emergency",
      question: "What constitutes an emergency service call?",
      answer: "Emergency services include: vehicle won't start and you're stranded, complete electrical failure, engine overheating with steam, brake warning lights, strange noises with safety concerns, and any situation where the vehicle is unsafe to drive. Non-urgent issues like routine maintenance should be scheduled as regular appointments.",
      tags: ["emergency", "urgent", "stranded", "safety", "breakdown"]
    },
    {
      category: "emergency",
      question: "How much extra do emergency services cost?",
      answer: "Emergency services during business hours (7 AM - 7 PM) have no additional charge. After-hours emergency calls (7 PM - 7 AM) and weekends have an additional KES 500 emergency response fee. This covers the priority dispatch and technician availability outside normal hours.",
      tags: ["emergency", "cost", "after hours", "weekend", "fee"]
    },

    // Technical & Equipment
    {
      category: "technical",
      question: "Can you diagnose modern computerized vehicles?",
      answer: "Yes! Our technicians are trained and equipped to work with modern computerized vehicles including hybrid systems, advanced driver assistance systems (ADAS), electronic control units (ECUs), and complex electrical systems. We have specialized diagnostic software for various makes including European, Japanese, and American vehicles.",
      tags: ["modern", "computerized", "hybrid", "ecu", "adas"]
    },
    {
      category: "technical",
      question: "Do you work on diesel vehicles and trucks?",
      answer: "Absolutely! We specialize in diesel engine diagnostics including common rail systems, turbocharger diagnostics, DPF (Diesel Particulate Filter) issues, and commercial vehicle systems. Our equipment can handle light commercial vehicles, pickup trucks, and small to medium commercial trucks up to 7.5 tons.",
      tags: ["diesel", "trucks", "commercial", "turbo", "dpf"]
    },
    {
      category: "technical",
      question: "What if my vehicle has a rare or complex problem?",
      answer: "Our experienced technicians can handle most complex issues. If we encounter a problem requiring specialized equipment or expertise beyond our mobile capabilities, we'll provide a detailed diagnosis and refer you to our network of trusted specialist workshops with transparent pricing and quality guarantees.",
      tags: ["complex", "rare", "specialist", "referral", "expertise"]
    },

    // Warranty & Guarantees
    {
      category: "warranty",
      question: "Do you provide warranties on your diagnostic services?",
      answer: "Yes! We offer a 100% satisfaction guarantee on all diagnostic services. If you're not completely satisfied, we'll make it right or provide a full refund. For repairs performed on-site, we provide a 30-day warranty on parts and labor. Diagnostic reports are guaranteed accurate based on the vehicle's condition at time of service.",
      tags: ["warranty", "guarantee", "satisfaction", "refund", "repair"]
    },
    {
      category: "warranty",
      question: "What happens if the diagnosis is incorrect?",
      answer: "While our diagnostic accuracy rate is over 95%, if our diagnosis proves incorrect, we'll return to re-diagnose at no additional charge and refund the original diagnostic fee if the error was on our part. We stand behind our work and maintain detailed service records for quality assurance.",
      tags: ["incorrect", "accuracy", "refund", "re-diagnose", "quality"]
    },
    {
      category: "warranty",
      question: "Do you guarantee your emergency response times?",
      answer: "Yes! We guarantee arrival within 60 minutes for emergency calls within our primary service area, or your diagnostic fee is waived. For scheduled appointments, we guarantee arrival within the agreed time window or provide a service credit for future use.",
      tags: ["guarantee", "response time", "emergency", "arrival", "credit"]
    }
  ];

  const quickActions = [
    {
      title: "Book Service Now",
      description: "Schedule your diagnostic service",
      icon: Calendar,
      link: "/booking",
      color: "bg-automotive-blue"
    },
    {
      title: "Emergency Hotline",
      description: "24/7 emergency assistance",
      icon: Phone,
      link: "tel:+254727795520",
      color: "bg-red-600"
    },
    {
      title: "WhatsApp Support",
      description: "Quick questions & booking",
      icon: MessageCircle,
      link: "https://wa.me/254727795520",
      color: "bg-green-600"
    },
    {
      title: "Contact Us",
      description: "General inquiries",
      icon: HelpCircle,
      link: "/contact",
      color: "bg-automotive-orange"
    }
  ];

  // Filter FAQs based on search term and selected category
  const filteredFAQs = useMemo(() => {
    return faqs.filter(faq => {
      const matchesSearch = searchTerm === "" ||
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === "All" || faq.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, selectedCategory, faqs]);

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-automotive-dark to-automotive-blue text-white py-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-6">
                Help Center
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold mb-6">
                Frequently Asked
                <span className="text-automotive-orange"> Questions</span>
              </h1>
              <p className="text-xl text-primary-foreground mb-8 leading-relaxed">
                Find answers to common questions about our mobile automotive diagnostic services in Nairobi.
                Use the search below or browse by category.
              </p>

              {/* Search Bar */}
              <div className="max-w-2xl mx-auto">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder="Search FAQs... (e.g., 'pricing', 'emergency', 'Toyota')"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 py-3 text-lg bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:bg-white/20"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-8 bg-gray-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {quickActions.map((action, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer">
                    <CardContent className="p-4 text-center">
                      {action.link.startsWith('http') || action.link.startsWith('tel:') ? (
                        <a href={action.link} className="block">
                          <div className={`w-12 h-12 ${action.color} rounded-full flex items-center justify-center mx-auto mb-2`}>
                            <action.icon className="w-6 h-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-automotive-dark text-sm mb-1">{action.title}</h3>
                          <p className="text-xs text-muted-foreground">{action.description}</p>
                        </a>
                      ) : (
                        <Link to={action.link} className="block">
                          <div className={`w-12 h-12 ${action.color} rounded-full flex items-center justify-center mx-auto mb-2`}>
                            <action.icon className="w-6 h-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-automotive-dark text-sm mb-1">{action.title}</h3>
                          <p className="text-xs text-muted-foreground">{action.description}</p>
                        </Link>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Categories & Content */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            {/* Category Filters */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-automotive-dark text-center mb-8">Browse by Category</h2>
              <div className="flex flex-wrap justify-center gap-4 mb-8">
                <Button
                  variant={selectedCategory === "All" ? "default" : "outline"}
                  onClick={() => setSelectedCategory("All")}
                  className={selectedCategory === "All" ? "bg-automotive-blue" : ""}
                >
                  All Questions ({faqs.length})
                </Button>
                {faqCategories.map((category) => {
                  const categoryCount = faqs.filter(faq => faq.category === category.id).length;
                  return (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`flex items-center gap-2 ${selectedCategory === category.id ? "bg-automotive-blue" : ""}`}
                    >
                      <category.icon className="w-4 h-4" />
                      {category.name} ({categoryCount})
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Search Results Info */}
            {searchTerm && (
              <div className="mb-6 text-center">
                <p className="text-muted-foreground">
                  Found {filteredFAQs.length} result{filteredFAQs.length !== 1 ? 's' : ''} for "{searchTerm}"
                </p>
              </div>
            )}

            {/* FAQ Content */}
            <div className="grid lg:grid-cols-3 gap-12">
              {/* FAQ List */}
              <div className="lg:col-span-2">
                <AnimatePresence>
                  {filteredFAQs.length > 0 ? (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                    >
                      <Accordion type="single" collapsible className="space-y-4">
                        {filteredFAQs.map((faq, index) => {
                          const category = faqCategories.find(cat => cat.id === faq.category);
                          return (
                            <AccordionItem
                              key={`${faq.category}-${index}`}
                              value={`item-${faq.category}-${index}`}
                              className="border border-gray-200 rounded-lg overflow-hidden hover:border-automotive-blue/50 transition-colors"
                            >
                              <AccordionTrigger className="text-left px-6 py-4 hover:bg-gray-50 [&[data-state=open]]:bg-automotive-blue/5">
                                <div className="flex items-start gap-3 w-full">
                                  {category && (
                                    <div className="flex-shrink-0 mt-1">
                                      <category.icon className={`w-5 h-5 ${category.color}`} />
                                    </div>
                                  )}
                                  <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-automotive-dark pr-4">
                                      {faq.question}
                                    </h3>
                                    {category && (
                                      <Badge variant="secondary" className="mt-2 text-xs">
                                        {category.name}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent className="px-6 pb-6">
                                <div className="pl-8">
                                  <p className="text-muted-foreground leading-relaxed">
                                    {faq.answer}
                                  </p>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          );
                        })}
                      </Accordion>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-12"
                    >
                      <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-automotive-dark mb-2">No results found</h3>
                      <p className="text-muted-foreground mb-4">
                        Try adjusting your search terms or browse different categories.
                      </p>
                      <Button
                        onClick={() => {
                          setSearchTerm("");
                          setSelectedCategory("All");
                        }}
                        variant="outline"
                      >
                        Clear Search
                      </Button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Contact Sidebar */}
              <div className="space-y-6">
                {/* Contact Card */}
                <Card className="bg-gradient-to-br from-automotive-blue to-automotive-dark text-white border-0">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-white">
                      <HelpCircle className="w-6 h-6" />
                      Still Have Questions?
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white/90 mb-6">
                      Our expert team is ready to help with any automotive diagnostic questions.
                    </p>
                    <div className="space-y-3">
                      <Button
                        size="lg"
                        className="w-full bg-automotive-orange hover:bg-automotive-orange/90"
                        asChild
                      >
                        <a href="tel:+254727795520">
                          <Phone className="w-5 h-5 mr-2" />
                          Call: 0727 795 520
                        </a>
                      </Button>
                      <Button
                        size="lg"
                        variant="outline"
                        className="w-full border-white text-white hover:bg-white/10"
                        asChild
                      >
                        <Link to="/contact">
                          <MessageCircle className="w-5 h-5 mr-2" />
                          Send Message
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Service Info Card */}
                <Card className="border border-automotive-orange/20">
                  <CardHeader>
                    <CardTitle className="text-automotive-dark">Quick Service Info</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Clock className="w-5 h-5 text-automotive-blue" />
                        <div>
                          <p className="font-medium text-automotive-dark">Response Time</p>
                          <p className="text-sm text-muted-foreground">15-45 minutes average</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <MapPin className="w-5 h-5 text-automotive-orange" />
                        <div>
                          <p className="font-medium text-automotive-dark">Service Area</p>
                          <p className="text-sm text-muted-foreground">Greater Nairobi area</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                        <div>
                          <p className="font-medium text-automotive-dark">Emergency</p>
                          <p className="text-sm text-muted-foreground">24/7 availability</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <CreditCard className="w-5 h-5 text-green-600" />
                        <div>
                          <p className="font-medium text-automotive-dark">Starting Price</p>
                          <p className="text-sm text-muted-foreground">From KES 1,500</p>
                        </div>
                      </div>
                    </div>
                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <Button
                        className="w-full bg-automotive-blue hover:bg-automotive-blue/90"
                        asChild
                      >
                        <Link to="/booking">
                          Book Service Now
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Trust Indicators */}
                <Card className="bg-green-50 border border-green-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <Shield className="w-6 h-6 text-green-600" />
                      <h4 className="font-bold text-green-800">Service Guarantees</h4>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-green-700">100% satisfaction guarantee</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-green-700">30-day warranty on repairs</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-green-700">Licensed & insured technicians</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-green-700">Transparent pricing</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Pro Tip */}
                <Card className="bg-blue-50 border border-blue-200">
                  <CardContent className="p-6">
                    <h4 className="font-bold text-blue-800 mb-2 flex items-center gap-2">
                      💡 Pro Tip
                    </h4>
                    <p className="text-sm text-blue-700">
                      For faster service, have your vehicle registration, any error codes, and a description
                      of symptoms ready when you call. This helps our technicians prepare the right tools.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Bottom CTA Section */}
        <section className="py-16 bg-gradient-to-r from-automotive-dark to-automotive-blue text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold mb-4">Ready to Get Your Vehicle Diagnosed?</h2>
              <p className="text-xl text-white/90 mb-8">
                Don't let vehicle problems stress you out. Our mobile diagnostic service brings
                professional expertise directly to your location in Nairobi.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-automotive-orange hover:bg-automotive-orange/90 text-white px-8 py-3"
                  asChild
                >
                  <Link to="/booking">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Diagnostic Service
                  </Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white/10 px-8 py-3"
                  asChild
                >
                  <a href="tel:+254727795520">
                    <Phone className="w-5 h-5 mr-2" />
                    Call for Emergency
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default FAQ;
