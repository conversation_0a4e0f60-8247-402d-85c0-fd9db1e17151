import React from "react";
import { <PERSON> } from "react-router-dom";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { HelpCircle, ArrowRight } from "lucide-react";
import WhatsAppButton from "@/components/home/<USER>";

const FAQ = () => {
  const faqs = [
    {
      question: "What areas do you cover for mobile diagnostic services?",
      answer: "We serve Nairobi and surrounding areas. We come to you – home, office, or roadside in Nairobi and nearby areas. Please contact us with your specific location to confirm service availability.",
    },
    {
      question: "How quickly can a technician come to my location?",
      answer: "For emergency breakdowns, we aim for a 15-minute response time within our primary service areas. For scheduled appointments, we offer flexible booking options to suit your convenience.",
    },
    {
      question: "What types of vehicles do you service?",
      answer: "Our technicians are equipped to diagnose and service a wide range of vehicles, including sedans, SUVs, trucks, and commercial fleet vehicles. We handle most makes and models.",
    },
    {
      question: "Do you offer on-the-spot repairs after diagnosis?",
      answer: "Yes, whenever possible! Our mobile units are equipped to perform many common repairs immediately after diagnosis, saving you time and the hassle of a separate garage visit. For more complex issues, we'll provide a detailed report and recommendations.",
    },
    {
      question: "How much does a diagnostic service cost?",
      answer: "Our diagnostic services start from KES 1,000 for basic OBD code reading, with comprehensive diagnostics ranging from KES 2,000 to KES 3,500 depending on the complexity and vehicle type. We always provide transparent pricing before beginning any work.",
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept cash, M-Pesa, credit/debit cards, and bank transfers for your convenience.",
    },
    {
      question: "Do I need to be present during the diagnostic service?",
      answer: "While it's not always necessary, we recommend being present so our technician can explain findings directly. However, we can also perform the service without you present if you provide access to the vehicle.",
    },
    {
      question: "How long does a typical diagnostic service take?",
      answer: "Most diagnostic services take between 30-60 minutes, depending on the complexity of the issue. Some comprehensive inspections may take longer.",
    },
    {
      question: "Do you provide a warranty on your services?",
      answer: "Yes, we stand behind our work with a 30-day warranty on all diagnostic services and repairs performed by our technicians.",
    },
    {
      question: "What happens if my vehicle needs parts replaced?",
      answer: "If your vehicle requires parts, our technician will provide a detailed quote. With your approval, we can either source the parts for you or you can provide your own parts for installation.",
    }
  ];

  const categories = [
    "Service Coverage", "Pricing", "Booking", "Technical", "Payment", "Warranty"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-automotive-dark to-automotive-blue text-white py-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-6">
                Help Center
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold mb-6">
                Frequently Asked
                <span className="text-automotive-orange"> Questions</span>
              </h1>
              <p className="text-xl text-primary-foreground mb-8 leading-relaxed">
                Find answers to common questions about our mobile diagnostic services, booking process, and more.
              </p>
            </div>
          </div>
        </section>

        {/* FAQ Categories */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category, index) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className="px-4 py-2 text-base cursor-pointer hover:bg-automotive-blue/10 transition-colors"
                >
                  {category}
                </Badge>
              ))}
            </div>

            {/* FAQ Accordion */}
            <div className="max-w-3xl mx-auto">
              <Accordion type="single" collapsible className="space-y-4">
                {faqs.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`} className="border border-border rounded-lg px-6">
                    <AccordionTrigger className="text-lg font-medium text-automotive-dark hover:text-automotive-blue">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>

            {/* Still Have Questions */}
            <div className="mt-16 text-center">
              <div className="inline-flex justify-center items-center w-16 h-16 bg-automotive-blue/10 rounded-full mb-4">
                <HelpCircle className="w-8 h-8 text-automotive-blue" />
              </div>
              <h3 className="text-2xl font-bold text-automotive-dark mb-4">Still Have Questions?</h3>
              <p className="text-muted-foreground mb-6 max-w-xl mx-auto">
                Our customer support team is ready to assist you with any other questions you might have about our services.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-automotive-orange hover:bg-automotive-orange/90">
                  <Link to="/contact">
                    Contact Us
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline">
                  <Link to="/booking">Book a Service</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default FAQ;
