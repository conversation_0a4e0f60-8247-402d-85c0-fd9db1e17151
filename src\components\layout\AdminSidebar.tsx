
"use client";

import React from "react";
import { Link, useLocation } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import {
  LayoutDashboard,
  Calendar,
  Users,
  BarChart3,
  Package,
  UserCog,
  Activity,
  Settings,
  LogOut,
} from "lucide-react";
import { Button } from "@/components/ui/button";

// Mock user role - in real app this would come from auth context
const userRole = "Admin"; // Could be "Technician", "Admin", "Manager"
const userName = "<PERSON>";

const menuItems = [
  {
    title: "Dashboard",
    url: "/admin",
    icon: LayoutDashboard,
    roles: ["Technician", "Admin", "Manager"]
  },
  {
    title: "Bookings",
    url: "/admin/bookings",
    icon: Calendar,
    roles: ["Technician", "Admin", "Manager"]
  },
  {
    title: "Customers",
    url: "/admin/customers",
    icon: Users,
    roles: ["Admin", "Manager"]
  },
  {
    title: "Reports / Analytics",
    url: "/admin/reports",
    icon: BarChart3,
    roles: ["Admin", "Manager"]
  },
  {
    title: "Inventory",
    url: "/admin/inventory",
    icon: Package,
    roles: ["Admin", "Manager"]
  },
  {
    title: "User Management",
    url: "/admin/users",
    icon: UserCog,
    roles: ["Manager"]
  },
  {
    title: "Activity Logs",
    url: "/admin/logs",
    icon: Activity,
    roles: ["Admin", "Manager"]
  },
  {
    title: "Settings",
    url: "/admin/settings",
    icon: Settings,
    roles: ["Technician", "Admin", "Manager"]
  }
];

const AppSidebar = () => {
  const location = useLocation();
  
  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(userRole)
  );

  return (
    <Sidebar className="bg-white border-r border-gray-200">
      <SidebarHeader className="p-6 border-b border-gray-100">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-automotive-blue to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-lg">DW</span>
          </div>
          <div>
            <h2 className="font-bold text-lg text-automotive-dark">Admin Console</h2>
            <p className="text-sm text-gray-600">Welcome, {userName}</p>
          </div>
        </div>
        <div className="mt-4 px-3 py-2 bg-automotive-blue/10 rounded-lg">
          <span className="text-xs font-semibold text-automotive-blue uppercase tracking-wide">
            {userRole} Access
          </span>
        </div>
      </SidebarHeader>
      
      <SidebarContent className="px-4 py-6">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-4">
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-2">
              {filteredMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild
                    isActive={location.pathname === item.url}
                    className="w-full justify-start px-4 py-3 rounded-lg hover:bg-automotive-blue/10 hover:text-automotive-blue transition-all duration-200 data-[active=true]:bg-automotive-blue data-[active=true]:text-white data-[active=true]:shadow-lg"
                  >
                    <Link to={item.url} className="flex items-center space-x-3">
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      
      <SidebarFooter className="p-6 border-t border-gray-100">
        <Button 
          variant="outline" 
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300 font-medium"
          asChild
        >
          <Link to="/login">
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </Link>
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
};

export { AppSidebar };
