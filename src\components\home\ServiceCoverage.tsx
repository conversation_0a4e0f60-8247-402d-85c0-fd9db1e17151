
import React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { 
  MapPin, 
  Car, 
  Clock, 
  Building, 
  Home, 
  ShoppingBag, 
  Briefcase,
  ArrowRight,
  Truck,
  Tractor,
  CheckCircle,
  Phone,
  Navigation,
  Info
} from "lucide-react";
import { Link } from "react-router-dom";

const ServiceCoverage = () => {
  const locations = [
    {
      icon: Home,
      title: "Residential Areas",
      description: "Service at your home in Nairobi estates and suburbs",
      response: "20-40 min response",
      examples: ["<PERSON>", "Lavington", "Runda", "Kileleshwa"]
    },
    {
      icon: Building,
      title: "Office & Business Districts",
      description: "Service at your workplace in Nairobi's commercial areas",
      response: "15-30 min response",
      examples: ["Westlands", "Upperhill", "CBD", "Kilimani"]
    },
    {
      icon: ShoppingBag,
      title: "Shopping Malls & Centers",
      description: "While you shop at Nairobi's popular malls",
      response: "15-30 min response",
      examples: ["Two Rivers", "Garden City", "The Junction", "Sarit Centre"]
    },
    {
      icon: Briefcase,
      title: "Industrial Areas",
      description: "Service at Nairobi's industrial zones and business parks",
      response: "25-45 min response",
      examples: ["Industrial Area", "Enterprise Road", "Baba Dogo", "Ruaraka"]
    }
  ];

  const vehicleTypes = [
    {
      icon: Car,
      type: "Passenger Vehicles",
      brands: [
        "Toyota (Corolla, Vitz, Premio, Fielder)",
        "Mazda (Demio, Axela, CX-5)",
        "Honda (Fit, CR-V, Civic)",
        "Nissan (X-Trail, Tiida, Note, Juke)",
        "Subaru (Forester, Impreza, Outback)",
        "Volkswagen (Golf, Polo, Tiguan)",
        "Mercedes-Benz (C-Class, E-Class)",
        "BMW (3 Series, X5, X3)"
      ],
      services: [
        "Engine Diagnostics & Check Engine Light",
        "Electrical System & Battery Checks",
        "Transmission & Gearbox Diagnostics",
        "Brake System Analysis & ABS Troubleshooting",
        "Computer System Scans & ECU Programming",
        "Fuel System Diagnostics",
        "Air Conditioning System Checks"
      ],
      popularModels: ["Toyota Corolla", "Mazda Demio", "Subaru Forester", "Nissan X-Trail"]
    },
    {
      icon: Truck,
      type: "SUVs & Pickup Trucks",
      brands: [
        "Toyota (Land Cruiser, Hilux, Prado, RAV4)",
        "Mitsubishi (Pajero, L200, Outlander)",
        "Isuzu (D-Max, MU-X)",
        "Ford (Ranger, Everest)",
        "Nissan (Navara, Patrol)",
        "Land Rover (Discovery, Range Rover)",
        "Suzuki (Jimny, Grand Vitara)",
        "Volkswagen (Amarok, Touareg)"
      ],
      services: [
        "4x4 System Diagnostics",
        "Off-Road Performance Checks",
        "Towing Capacity Assessment",
        "Suspension & Shock Diagnostics",
        "Diesel Engine Diagnostics",
        "Turbocharger System Analysis",
        "Heavy-Duty Electrical Systems"
      ],
      popularModels: ["Toyota Hilux", "Toyota Land Cruiser", "Isuzu D-Max", "Mitsubishi Pajero"]
    },
    {
      icon: Tractor,
      type: "Commercial Vehicles",
      brands: [
        "Toyota (Hiace, Coaster, Probox)",
        "Nissan (Caravan, Urvan)",
        "Mitsubishi (Canter, Fuso)",
        "Isuzu (NQR, NPR, FRR, ELF)",
        "Mercedes-Benz (Actros, Sprinter)",
        "Scania (P-series, G-series)",
        "Volkswagen (Transporter, Caddy)",
        "Hino (300 Series, 500 Series)"
      ],
      services: [
        "Fleet Diagnostics & Management",
        "Commercial Vehicle Compliance Checks",
        "Heavy-Duty Diesel Engine Diagnostics",
        "Air Brake System Analysis",
        "Commercial Electrical Systems",
        "Transmission & Gearbox Diagnostics",
        "Fuel Efficiency Optimization"
      ],
      popularModels: ["Toyota Hiace", "Isuzu ELF", "Mitsubishi Canter", "Nissan Caravan"]
    }
  ];

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemFadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <section className="py-24 bg-gradient-to-b from-white to-automotive-gray/5">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          className="text-center mb-20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeIn}
        >
          <Badge variant="outline" className="border-automotive-blue text-automotive-blue mb-4 px-3 py-1 text-sm">
            Service Coverage
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-automotive-dark mb-6">
            We Come To
            <span className="text-automotive-orange"> Your Location</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Our mobile diagnostic service covers the entire Nairobi metropolitan area.
            Whether you're at home, work, or stranded on the road, we'll come to you.
          </p>
        </motion.div>

        {/* Service Locations */}
        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-24"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          {locations.map((location, index) => (
            <motion.div
              key={index}
              variants={itemFadeIn}
              className="group"
            >
              <Card className="border-0 shadow-md hover:shadow-xl transition-all duration-300 h-full overflow-hidden">
                <CardContent className="p-0">
                  <div className="p-8 flex flex-col items-center text-center">
                    <div className="w-16 h-16 bg-automotive-blue rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                      <location.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="space-y-3">
                      <h3 className="text-xl font-bold text-automotive-dark">{location.title}</h3>
                      <p className="text-base text-foreground">{location.description}</p>
                      <div className="flex items-center justify-center mt-2 bg-automotive-blue/10 py-2 px-4 rounded-full">
                        <Clock className="w-4 h-4 text-automotive-blue mr-2" />
                        <p className="text-sm font-medium text-automotive-blue">{location.response}</p>
                      </div>
                      <div className="flex flex-wrap justify-center gap-1 mt-2">
                        {location.examples.map((example, i) => (
                          <Badge 
                            key={i} 
                            variant="outline" 
                            className="text-xs bg-gray-50 border-gray-200"
                          >
                            {example}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Vehicle Types */}
        <motion.div 
          className="mb-24"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeIn}
        >
          <h3 className="text-3xl font-bold text-automotive-dark text-center mb-12">
            Supported Vehicle Types in Kenya
          </h3>
          
          <motion.div 
            className="grid lg:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
          >
            {vehicleTypes.map((vehicle, index) => (
              <motion.div key={index} variants={itemFadeIn}>
                <Card className="hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden group h-full">
                  <CardHeader className="pb-2 pt-6 px-6">
                    <div className="flex items-center space-x-4 mb-2">
                      <div className="w-14 h-14 bg-automotive-blue rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <vehicle.icon className="w-7 h-7 text-white" />
                      </div>
                      <CardTitle className="text-2xl font-bold text-automotive-dark group-hover:text-automotive-blue transition-colors">
                        {vehicle.type}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="px-6 pb-6">
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-semibold text-automotive-dark mb-3 flex items-center">
                          <Car className="w-4 h-4 text-automotive-orange mr-2" />
                          Popular Models in Kenya
                        </h4>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {vehicle.popularModels.map((model, modelIndex) => (
                            <Badge 
                              key={modelIndex} 
                              className="text-xs py-1 px-3 bg-automotive-orange/10 text-automotive-orange border-0"
                            >
                              {model}
                            </Badge>
                          ))}
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {vehicle.brands.slice(0, 4).map((brand, brandIndex) => (
                            <Badge 
                              key={brandIndex} 
                              variant="secondary" 
                              className="text-xs py-1 px-3 bg-automotive-gray/10 hover:bg-automotive-gray/20 transition-colors"
                            >
                              {brand}
                            </Badge>
                          ))}
                        </div>
                        {vehicle.brands.length > 4 && (
                          <Button variant="link" className="text-xs p-0 h-auto mt-2 text-automotive-blue">
                            +{vehicle.brands.length - 4} more brands
                          </Button>
                        )}
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-automotive-dark mb-3 flex items-center">
                          <CheckCircle className="w-4 h-4 text-automotive-orange mr-2" />
                          Services Available
                        </h4>
                        <ul className="space-y-2">
                          {vehicle.services.map((service, serviceIndex) => (
                            <li key={serviceIndex} className="flex items-center space-x-2 text-sm text-muted-foreground group/item">
                              <div className="w-1.5 h-1.5 rounded-full bg-automotive-blue flex-shrink-0 group-hover/item:scale-125 transition-transform"></div>
                              <span className="group-hover/item:text-automotive-dark transition-colors">{service}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Interactive Map Section */}
        <motion.div 
          className="rounded-2xl overflow-hidden"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeIn}
        >
          <div className="bg-gradient-to-br from-automotive-dark to-automotive-blue p-10 md:p-16 text-white">
            <div className="max-w-5xl mx-auto">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-10">
                <div className="mb-6 md:mb-0 md:max-w-xl">
                  <h3 className="text-3xl md:text-4xl font-bold mb-4">Nairobi Service Area</h3>
                  <p className="text-primary-foreground text-lg leading-relaxed">
                    We proudly serve Nairobi and its surrounding areas with fast, reliable mobile automotive diagnostic services.
                    Check if your location is within our service area.
                  </p>
                </div>
                
                <div className="flex flex-wrap gap-3">
                  <Button asChild size="lg" className="bg-automotive-orange hover:bg-automotive-orange/90 group">
                    <a href="tel:+254727795520">
                      <Phone className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                      Check Availability
                    </a>
                  </Button>
                  <Button variant="outline" size="lg" className="border-white/20 text-white hover:bg-white/10 group">
                    <MapPin className="w-5 h-5 mr-2 group-hover:translate-y-[-2px] transition-transform" />
                    View Coverage
                  </Button>
                </div>
              </div>
              
              {/* Map Visualization */}
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-8 md:p-12 relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-r from-automotive-blue/20 to-automotive-orange/20 opacity-50"></div>
                
                <div className="relative z-10">
                  <div className="flex justify-center mb-8">
                    <div className="w-20 h-20 rounded-full bg-automotive-orange/20 flex items-center justify-center animate-pulse">
                      <MapPin className="w-10 h-10 text-automotive-orange" />
                    </div>
                  </div>
                  
                  <div className="text-center mb-8">
                    <h4 className="text-2xl font-bold mb-4">Nairobi Metropolitan Area</h4>
                    <p className="text-primary-foreground max-w-2xl mx-auto">
                      Click to view detailed coverage areas and estimated response times for your location in Nairobi
                    </p>
                  </div>
                  
                  {/* Nairobi Areas Grid */}
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 max-w-4xl mx-auto">
                    {[
                      { name: "Westlands", response: "15-30 min" },
                      { name: "Kilimani", response: "15-30 min" },
                      { name: "Karen", response: "20-35 min" },
                      { name: "Lavington", response: "15-30 min" },
                      { name: "Kileleshwa", response: "15-30 min" },
                      { name: "Parklands", response: "15-30 min" },
                      { name: "Gigiri", response: "20-35 min" },
                      { name: "Runda", response: "20-35 min" },
                      { name: "Muthaiga", response: "20-35 min" },
                      { name: "CBD", response: "20-40 min" },
                      { name: "Upperhill", response: "20-35 min" },
                      { name: "Ngong Road", response: "15-30 min" },
                      { name: "Langata", response: "20-40 min" },
                      { name: "South B/C", response: "25-45 min" },
                      { name: "Embakasi", response: "30-50 min" },
                      { name: "Kasarani", response: "30-50 min" },
                      { name: "Thika Road", response: "25-45 min" },
                      { name: "Eastleigh", response: "25-45 min" },
                      { name: "Kitengela", response: "40-60 min" },
                      { name: "Ongata Rongai", response: "35-55 min" }
                    ].map((area, index) => (
                      <div 
                        key={index}
                        className="bg-white/10 hover:bg-white/20 transition-colors rounded-lg p-3 text-center cursor-pointer group/city"
                      >
                        <div className="flex flex-col items-center justify-center">
                          <div className="flex items-center mb-1">
                            <Navigation className="w-4 h-4 mr-1 text-automotive-orange group-hover/city:rotate-45 transition-transform" />
                            <span className="text-sm font-medium">{area.name}</span>
                          </div>
                          <span className="text-xs text-white/70">{area.response}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Nairobi Map */}
                  <div className="mt-10 rounded-lg overflow-hidden shadow-lg">
                    <iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d255281.19086527423!2d36.70730844863279!3d-1.3028602763693747!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f1172d84d49a7%3A0xf7cf0254b297924c!2sNairobi%2C%20Kenya!5e0!3m2!1sen!2ske!4v1678886500000!5m2!1sen!2ske"
                      width="100%"
                      height="300"
                      style={{ border: 0 }}
                      allowFullScreen={true}
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title="Nairobi Service Area Map"
                      className="w-full"
                    ></iframe>
                  </div>
                </div>
              </div>
              
              {/* Service Area Info */}
              <div className="mt-8 flex flex-col sm:flex-row items-center justify-between bg-white/10 rounded-xl p-5">
                <div className="flex items-center mb-4 sm:mb-0">
                  <Info className="w-5 h-5 text-automotive-orange mr-2" />
                  <p className="text-sm">
                    Not sure if we service your area in Nairobi? Contact us for confirmation.
                  </p>
                </div>
                <Button asChild size="sm" className="bg-white text-automotive-blue hover:bg-white/90">
                  <Link to="/contact">
                    Contact Us
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServiceCoverage;









