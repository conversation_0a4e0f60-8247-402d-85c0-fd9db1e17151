import React, { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, ArrowRight, CheckCircle, CalendarIcon, Clock, Info } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format, isWeekend, addDays, isBefore, isToday } from "date-fns";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";

const availableServices = [
  { id: "engine_diagnostics", name: "Engine Diagnostics", price: "KES 2,000" },
  { id: "full_system_scan", name: "Full System Scan", price: "KES 3,000" },
  { id: "battery_charging_test", name: "Battery & Charging Test", price: "KES 1,500" },
  { id: "pre_purchase_inspection", name: "Pre-Purchase Inspection", price: "KES 3,500" },
  { id: "cooling_system_check", name: "Cooling System Check", price: "KES 2,000" },
  { id: "transmission_diagnostics", name: "Transmission Diagnostics", price: "KES 2,500" },
  { id: "brake_system_check", name: "Brake System Check", price: "KES 1,800" },
  { id: "fleet_diagnostics", name: "Fleet Diagnostics", price: "From KES 5,000" },
  { id: "service_light_reset", name: "Service Light Reset", price: "KES 800" },
  { id: "obd_fault_code_interpretation", name: "OBD Fault Code Interpretation", price: "KES 1,000" },
];

const STAGE_TITLES = [
  "Stage 1: Select Your Service",
  "Stage 2: Vehicle Information",
  "Stage 3: Your Details & Location",
  "Stage 4: Review & Confirm Booking", // Updated title
];

const Stage1Form = ({ onNext, currentData }: { onNext: (data: any) => void; currentData: any }) => {
  const [selectedServices, setSelectedServices] = useState<string[]>(currentData?.services || []);

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices((prevSelected) =>
      prevSelected.includes(serviceId)
        ? prevSelected.filter((id) => id !== serviceId)
        : [...prevSelected, serviceId]
    );
  };

  const handleNextClick = () => {
    if (selectedServices.length === 0) {
      alert("Please select at least one service."); // TODO: Use toast
      return;
    }
    onNext({ services: selectedServices });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-foreground mb-3">Select Services:</h3>
        <div className="space-y-3 max-h-60 overflow-y-auto pr-2 rounded-md border border-border p-4 bg-card">
          {availableServices.map((service) => (
            <div key={service.id} className="flex items-center space-x-3 p-2 hover:bg-muted rounded-md transition-colors">
              <Checkbox
                id={`service-${service.id}`}
                checked={selectedServices.includes(service.id)}
                onCheckedChange={() => handleServiceToggle(service.id)}
              />
              <Label htmlFor={`service-${service.id}`} className="flex-grow cursor-pointer text-sm font-medium text-foreground">
                {service.name}
              </Label>
              <span className="text-sm text-muted-foreground">{service.price}</span>
            </div>
          ))}
        </div>
        {selectedServices.length > 0 && (
          <p className="text-xs text-muted-foreground mt-2">
            Selected: {selectedServices.map(id => availableServices.find(s => s.id === id)?.name).join(', ')}
          </p>
        )}
      </div>
      <div className="flex justify-end mt-6">
        <Button 
          onClick={handleNextClick} 
          className="w-full sm:w-auto bg-automotive-orange hover:bg-automotive-orange/90 text-white"
        >
          Next <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const Stage2Form = ({ onNext, onPrev, currentData }: { onNext: (data: any) => void; onPrev: () => void; currentData: any }) => {
  const [vehicleMake, setVehicleMake] = useState(currentData?.vehicleMake || "");
  const [vehicleModel, setVehicleModel] = useState(currentData?.vehicleModel || "");
  const [vehicleYear, setVehicleYear] = useState(currentData?.vehicleYear || "");
  const [vehicleVIN, setVehicleVIN] = useState(currentData?.vehicleVIN || "");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!vehicleMake.trim()) newErrors.vehicleMake = "Vehicle make is required.";
    if (!vehicleModel.trim()) newErrors.vehicleModel = "Vehicle model is required.";
    if (!vehicleYear) {
      newErrors.vehicleYear = "Vehicle year is required.";
    } else {
      const yearNum = parseInt(vehicleYear, 10);
      if (isNaN(yearNum) || yearNum < 1980 || yearNum > new Date().getFullYear() + 1) {
        newErrors.vehicleYear = `Year must be a number between 1980 and ${new Date().getFullYear() + 1}.`;
      }
    }
    if (vehicleVIN.trim() && vehicleVIN.trim().length !== 17) {
      newErrors.vehicleVIN = "VIN must be 17 characters long if provided.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextClick = () => {
    if (validate()) {
      onNext({ vehicleMake, vehicleModel, vehicleYear, vehicleVIN });
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="vehicleMake" className="font-medium text-foreground">Vehicle Make</Label>
        <Input
          id="vehicleMake"
          value={vehicleMake}
          onChange={(e) => setVehicleMake(e.target.value)}
          placeholder="e.g., Toyota"
          required
          className={`mt-1 ${errors.vehicleMake ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.vehicleMake && <p className="text-xs text-destructive mt-1">{errors.vehicleMake}</p>}
      </div>
      <div>
        <Label htmlFor="vehicleModel" className="font-medium text-foreground">Vehicle Model</Label>
        <Input
          id="vehicleModel"
          value={vehicleModel}
          onChange={(e) => setVehicleModel(e.target.value)}
          placeholder="e.g., Corolla"
          required
          className={`mt-1 ${errors.vehicleModel ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.vehicleModel && <p className="text-xs text-destructive mt-1">{errors.vehicleModel}</p>}
      </div>
      <div>
        <Label htmlFor="vehicleYear" className="font-medium text-foreground">Vehicle Year</Label>
        <Input
          id="vehicleYear"
          type="number"
          value={vehicleYear}
          onChange={(e) => setVehicleYear(e.target.value)}
          placeholder="e.g., 2015"
          required
          min="1980"
          max={new Date().getFullYear() + 1}
          className={`mt-1 ${errors.vehicleYear ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.vehicleYear && <p className="text-xs text-destructive mt-1">{errors.vehicleYear}</p>}
      </div>
      <div>
        <Label htmlFor="vehicleVIN" className="font-medium text-foreground">Vehicle VIN <span className="text-sm text-muted-foreground">(Optional, 17 characters)</span></Label>
        <Input
          id="vehicleVIN"
          value={vehicleVIN}
          onChange={(e) => setVehicleVIN(e.target.value.toUpperCase())}
          placeholder="17-character Vehicle Identification Number"
          maxLength={17}
          className={`mt-1 ${errors.vehicleVIN ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.vehicleVIN && <p className="text-xs text-destructive mt-1">{errors.vehicleVIN}</p>}
      </div>
      <div className="flex flex-col sm:flex-row gap-3 pt-2 justify-between">
        <Button onClick={onPrev} variant="outline" className="w-full sm:w-auto">
          <ArrowLeft className="mr-2 h-4 w-4" /> Previous
        </Button>
        <Button 
          onClick={handleNextClick} 
          className="w-full sm:w-auto bg-automotive-orange hover:bg-automotive-orange/90 text-white"
        >
          Next <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const Stage3Form = ({ onNext, onPrev, currentData }: { onNext: (data: any) => void; onPrev: () => void; currentData: any }) => {
  const [fullName, setFullName] = useState(currentData?.fullName || "");
  const [phone, setPhone] = useState(currentData?.phone || "");
  const [email, setEmail] = useState(currentData?.email || "");
  const [address, setAddress] = useState(currentData?.address || "");
  const [city, setCity] = useState(currentData?.city || "Nairobi");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!fullName.trim()) newErrors.fullName = "Full name is required.";
    if (!phone.trim()) {
      newErrors.phone = "Phone number is required.";
    } else if (!/^(07|01)\d{8}$/.test(phone) && !/^\+254\d{9}$/.test(phone)) {
      newErrors.phone = "Please enter a valid Kenyan phone number (e.g., 07XXXXXXXX or +254XXXXXXXXX).";
    }
    if (!email.trim()) {
      newErrors.email = "Email address is required.";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Please enter a valid email address.";
    }
    if (!address.trim()) newErrors.address = "Address/Estate is required.";
    if (!city.trim()) newErrors.city = "City/Town is required.";
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextClick = () => {
    if (validate()) {
      onNext({ fullName, phone, email, address, city });
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="fullName" className="font-medium text-foreground">Full Name</Label>
        <Input
          id="fullName"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
          placeholder="e.g., John Doe"
          required
          className={`mt-1 ${errors.fullName ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.fullName && <p className="text-xs text-destructive mt-1">{errors.fullName}</p>}
      </div>
      <div>
        <Label htmlFor="phone" className="font-medium text-foreground">Phone Number</Label>
        <Input
          id="phone"
          type="tel"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          placeholder="e.g., 0712345678 or +254712345678"
          required
          className={`mt-1 ${errors.phone ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.phone && <p className="text-xs text-destructive mt-1">{errors.phone}</p>}
      </div>
      <div>
        <Label htmlFor="email" className="font-medium text-foreground">Email Address</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="e.g., <EMAIL>"
          required
          className={`mt-1 ${errors.email ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.email && <p className="text-xs text-destructive mt-1">{errors.email}</p>}
      </div>
      <div>
        <Label htmlFor="address" className="font-medium text-foreground">Address / Estate</Label>
        <Input
          id="address"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          placeholder="e.g., Uhuru Estate, Hse No. 123"
          required
          className={`mt-1 ${errors.address ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.address && <p className="text-xs text-destructive mt-1">{errors.address}</p>}
      </div>
      <div>
        <Label htmlFor="city" className="font-medium text-foreground">City / Town</Label>
        <Input
          id="city"
          value={city}
          onChange={(e) => setCity(e.target.value)}
          required
          className={`mt-1 ${errors.city ? 'border-destructive focus:border-destructive focus:ring-destructive' : 'focus:ring-brand-accent focus:border-brand-accent'}`}
        />
        {errors.city && <p className="text-xs text-destructive mt-1">{errors.city}</p>}
      </div>
      <div className="flex flex-col sm:flex-row gap-3 pt-2 justify-between">
        <Button onClick={onPrev} variant="outline" className="w-full sm:w-auto">
          <ArrowLeft className="mr-2 h-4 w-4" /> Previous
        </Button>
        <Button 
          onClick={handleNextClick} 
          className="w-full sm:w-auto bg-automotive-orange hover:bg-automotive-orange/90 text-white"
        >
          Next <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const Stage4Form = ({ onPrev, onSubmit, bookingData, currentData }: { onPrev: () => void; onSubmit: (data: any) => void; bookingData: any; currentData: any }) => {
  const [preferredDate, setPreferredDate] = useState<Date | undefined>(currentData?.preferredDate);
  const [preferredTime, setPreferredTime] = useState(currentData?.preferredTime || "");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Available time slots
  const timeSlots = [
    { id: "morning-early", label: "8:00 AM - 10:00 AM", value: "8:00 AM - 10:00 AM" },
    { id: "morning-late", label: "10:00 AM - 12:00 PM", value: "10:00 AM - 12:00 PM" },
    { id: "afternoon-early", label: "1:00 PM - 3:00 PM", value: "1:00 PM - 3:00 PM" },
    { id: "afternoon-late", label: "3:00 PM - 5:00 PM", value: "3:00 PM - 5:00 PM" },
  ];

  // Function to check if a date is available
  const isDateAvailable = (date: Date) => {
    // Example: Weekends have limited availability
    if (isWeekend(date)) {
      // Only morning slots on weekends in this example
      return "limited";
    }
    return "available";
  };

  // Function to check if a time slot is available for the selected date
  const isTimeSlotAvailable = (slot: string) => {
    if (!preferredDate) return false; // No date selected means no slots available
    
    // Example: Morning slots only on weekends
    if (isWeekend(preferredDate)) {
      return slot.includes("AM");
    }
    return true;
  };

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    setPreferredDate(date);
    
    // If the currently selected time is not available for the new date, clear it
    if (date && preferredTime && !isTimeSlotAvailable(preferredTime)) {
      setPreferredTime("");
    }
    
    // Clear date-related error when a date is selected
    if (date && errors.preferredDate) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.preferredDate;
        return newErrors;
      });
    }
  };

  // Handle time slot selection
  const handleTimeSelect = (value: string) => {
    setPreferredTime(value);
    
    // Clear time-related error when a time is selected
    if (value && errors.preferredTime) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.preferredTime;
        return newErrors;
      });
    }
  };

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!preferredDate) newErrors.preferredDate = "Preferred date is required.";
    if (!preferredTime) newErrors.preferredTime = "Preferred time slot is required.";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmitClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (validate()) {
      setIsSubmitting(true);
      onSubmit({ preferredDate, preferredTime });
    }
  };

  const getServiceName = (id: string) => availableServices.find(s => s.id === id)?.name || id;

  // Custom calendar day renderer to show availability
  const renderCalendarDay = (day: Date, modifiers: any) => {
    const availability = isDateAvailable(day);
    
    return (
      <div className={cn(
        "relative w-full h-full flex items-center justify-center",
        availability === "limited" && "after:absolute after:bottom-1 after:right-1 after:w-1.5 after:h-1.5 after:bg-amber-500 after:rounded-full",
        availability === "unavailable" && "after:absolute after:bottom-1 after:right-1 after:w-1.5 after:h-1.5 after:bg-red-500 after:rounded-full",
        isWeekend(day) && "bg-muted/50"
      )}>
        {day.getDate()}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-brand-primary mb-4">Review Your Booking Details</h3>
      
      <div className="space-y-4 p-4 border border-border rounded-lg bg-card">
        <div>
          <h4 className="font-semibold text-foreground">Selected Services:</h4>
          {bookingData.services && bookingData.services.length > 0 ? (
            <ul className="list-disc list-inside text-muted-foreground text-sm pl-4">
              {bookingData.services.map((serviceId: string) => <li key={serviceId}>{getServiceName(serviceId)}</li>)}
            </ul>
          ) : <p className="text-muted-foreground text-sm">No services selected.</p>}
        </div>
        <hr/>
        <div>
          <h4 className="font-semibold text-foreground">Vehicle Information:</h4>
          <p className="text-muted-foreground text-sm">Make: {bookingData.vehicleMake || "N/A"}</p>
          <p className="text-muted-foreground text-sm">Model: {bookingData.vehicleModel || "N/A"}</p>
          <p className="text-muted-foreground text-sm">Year: {bookingData.vehicleYear || "N/A"}</p>
          <p className="text-muted-foreground text-sm">VIN: {bookingData.vehicleVIN || "N/A"}</p>
        </div>
        <hr/>
        <div>
          <h4 className="font-semibold text-foreground">Your Details:</h4>
          <p className="text-muted-foreground text-sm">Name: {bookingData.fullName || "N/A"}</p>
          <p className="text-muted-foreground text-sm">Phone: {bookingData.phone || "N/A"}</p>
          <p className="text-muted-foreground text-sm">Email: {bookingData.email || "N/A"}</p>
          <p className="text-muted-foreground text-sm">Address: {bookingData.address || "N/A"}, {bookingData.city || "N/A"}</p>
        </div>
      </div>

      <div className="mt-8 border-t border-border pt-6">
        <h3 className="text-lg font-medium mb-4">Schedule Your Service</h3>
        
        <div className="space-y-6">
          <div>
            <div className="flex items-center mb-2">
              <Label htmlFor="preferredDate" className="font-medium text-foreground">Select Date</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Select your preferred service date. Weekends have limited availability (morning slots only).</p>
                    <ul className="mt-2 text-xs">
                      <li className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-automotive-orange mr-2"></div>
                        <span>Available</span>
                      </li>
                      <li className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-amber-500 mr-2"></div>
                        <span>Limited availability</span>
                      </li>
                    </ul>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            
            <div className="bg-card border border-border rounded-lg overflow-hidden">
              <Calendar
                mode="single"
                selected={preferredDate}
                onSelect={handleDateSelect}
                disabled={(date) => isBefore(date, addDays(new Date(), -1))}
                initialFocus
                className="rounded-md"
                classNames={{
                  day_selected: "bg-automotive-orange text-white hover:bg-automotive-orange hover:text-white focus:bg-automotive-orange focus:text-white",
                  day_today: "bg-muted text-foreground",
                }}
                components={{
                  Day: ({ date, ...props }) => (
                    <button {...props}>
                      {renderCalendarDay(date, props)}
                    </button>
                  ),
                }}
              />
            </div>
            {errors.preferredDate && <p className="text-xs text-destructive mt-1">{errors.preferredDate}</p>}
            
            {preferredDate && (
              <div className="mt-2 text-sm flex items-center">
                <CalendarIcon className="h-4 w-4 mr-2 text-automotive-orange" />
                <span>
                  Selected: <span className="font-medium">{format(preferredDate, "EEEE, MMMM d, yyyy")}</span>
                  {isWeekend(preferredDate) && (
                    <span className="ml-2 text-amber-600 text-xs">(Weekend - limited availability)</span>
                  )}
                </span>
              </div>
            )}
          </div>
          
          <div>
            <div className="flex items-center mb-2">
              <Label htmlFor="preferredTime" className="font-medium text-foreground">Select Time Slot</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Choose your preferred time slot. On weekends, only morning slots are available.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            
            <RadioGroup 
              value={preferredTime} 
              onValueChange={handleTimeSelect}
              className="grid grid-cols-1 sm:grid-cols-2 gap-3"
            >
              {timeSlots.map((slot) => {
                const isAvailable = isTimeSlotAvailable(slot.value);
                return (
                  <div key={slot.id} className={cn(
                    "relative",
                    !isAvailable && "opacity-50"
                  )}>
                    <RadioGroupItem
                      value={slot.value}
                      id={slot.id}
                      className="peer sr-only"
                      disabled={!isAvailable}
                    />
                    <Label
                      htmlFor={slot.id}
                      className={cn(
                        "flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4",
                        "hover:bg-accent hover:text-accent-foreground",
                        "peer-data-[state=checked]:border-automotive-orange peer-data-[state=checked]:bg-orange-50",
                        "transition-all",
                        isAvailable ? "cursor-pointer" : "cursor-not-allowed"
                      )}
                    >
                      <div className="flex items-center justify-center w-full">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{slot.label}</span>
                      </div>
                    </Label>
                  </div>
                );
              })}
            </RadioGroup>
            {!preferredDate && (
              <p className="text-xs text-muted-foreground mt-2">Please select a date first to see available time slots</p>
            )}
            {preferredDate && timeSlots.every(slot => !isTimeSlotAvailable(slot.value)) && (
              <p className="text-xs text-amber-600 mt-2">No time slots available for the selected date. Please choose another date.</p>
            )}
            {errors.preferredTime && <p className="text-xs text-destructive mt-1">{errors.preferredTime}</p>}
          </div>
        </div>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 pt-4 justify-between">
        <Button 
          onClick={onPrev} 
          variant="outline" 
          className="w-full sm:w-auto"
          disabled={isSubmitting}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Previous
        </Button>
        <Button 
          onClick={handleSubmitClick} 
          className="w-full sm:w-auto bg-automotive-orange hover:bg-automotive-orange/90 text-white"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
              Submitting...
            </>
          ) : (
            <>
              Confirm Booking <CheckCircle className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};


const BookingPage = () => {
  const [currentStage, setCurrentStage] = useState(0);
  const [bookingData, setBookingData] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  
  // Ref to store scroll position
  const scrollPositionRef = useRef(0);
  
  // Ref to the booking card element
  const bookingCardRef = useRef<HTMLDivElement>(null);

  // Function to save current scroll position
  const saveScrollPosition = useCallback(() => {
    scrollPositionRef.current = window.scrollY;
  }, []);

  // Function to restore saved scroll position
  const restoreScrollPosition = useCallback(() => {
    window.scrollTo({
      top: scrollPositionRef.current,
      behavior: 'auto' // Use auto to prevent visible scrolling
    });
  }, []);

  const handleNext = (stageData: any) => {
    // Only handle scrolling for normal navigation, not during submission
    if (!isSubmitting) {
      saveScrollPosition();
    }
    
    setBookingData((prev) => ({ ...prev, ...stageData }));
    
    if (currentStage < STAGE_TITLES.length - 1) {
      setCurrentStage((prev) => prev + 1);
      
      // Only scroll for normal navigation between stages
      if (!isSubmitting && bookingCardRef.current) {
        bookingCardRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      }
    }
  };

  const handlePrev = () => {
    if (currentStage > 0) {
      setCurrentStage((prev) => prev - 1);
    }
  };

  const handleSubmit = (finalData: any) => {
    // Save scroll position before any state changes
    saveScrollPosition();
    
    // Set submitting state to prevent unwanted scrolling
    setIsSubmitting(true);
    
    // Merge the final data with existing booking data
    const completeBookingData = { ...bookingData, ...finalData };
    
    // Simulate API call with a timeout
    setTimeout(() => {
      console.log("Final Booking Data:", completeBookingData);
      
      // Show toast notification
      toast.success("Booking Confirmed!", {
        description: "Your service has been scheduled successfully. You'll receive a confirmation email shortly.",
        duration: 5000,
        action: {
          label: "View Details",
          onClick: () => console.log("View booking details")
        },
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        className: "bg-white border-green-500",
        position: "top-center"
      });
      
      // Update states in a single batch to minimize re-renders
      setIsCompleted(true);
      setCurrentStage(STAGE_TITLES.length);
      
      // Use requestAnimationFrame to ensure scroll position is restored after DOM updates
      requestAnimationFrame(() => {
        restoreScrollPosition();
        setIsSubmitting(false);
      });
    }, 1500); // Simulate network delay
  };

  // Use effect to handle scroll position restoration after state changes
  useEffect(() => {
    if (isSubmitting) {
      // Prevent scrolling during submission by restoring position after any DOM updates
      const handleScroll = () => {
        if (isSubmitting) {
          restoreScrollPosition();
        }
      };
      
      window.addEventListener('scroll', handleScroll, { passive: true });
      
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isSubmitting, restoreScrollPosition]);

  const progressValue = ((currentStage + 1) / STAGE_TITLES.length) * 100;

  const stageVariants = {
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 },
  };

  return (
    <div className="min-h-screen bg-background font-inter">
      <Header />
      <main className="py-12 sm:py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-3xl">
          <Card ref={bookingCardRef} className="shadow-2xl border-border rounded-lg overflow-hidden card-booking">
            <CardHeader className="bg-muted border-b border-border p-6">
              <CardTitle className="text-2xl sm:text-3xl font-bold text-brand-primary font-urbanist text-center">
                Book Your Mobile Diagnostic Service
              </CardTitle>
              <CardDescription className="text-center text-muted-foreground mt-2">
                Follow these simple steps to schedule your appointment.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6 sm:p-8">
              {!isCompleted ? (
                <>
                  <div className="mb-8 space-y-3">
                    <h2 className="text-xl font-semibold text-brand-primary">{STAGE_TITLES[currentStage]}</h2>
                    <Progress value={progressValue} className="w-full h-2 [&>*]:bg-brand-accent" />
                    <p className="text-sm text-muted-foreground text-right">
                      Step {currentStage + 1} of {STAGE_TITLES.length}
                    </p>
                  </div>

                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentStage}
                      variants={stageVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      transition={{ duration: 0.3 }}
                    >
                      {currentStage === 0 && <Stage1Form onNext={handleNext} currentData={bookingData} />}
                      {currentStage === 1 && <Stage2Form onNext={handleNext} onPrev={handlePrev} currentData={bookingData} />}
                      {currentStage === 2 && <Stage3Form onNext={handleNext} onPrev={handlePrev} currentData={bookingData} />}
                      {currentStage === 3 && <Stage4Form onPrev={handlePrev} onSubmit={handleSubmit} bookingData={bookingData} currentData={bookingData} />}
                    </motion.div>
                  </AnimatePresence>
                </>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-2xl font-semibold text-brand-primary">Booking Confirmed!</h3>
                  <p className="text-muted-foreground mt-2">Thank you for your booking. We will contact you shortly to confirm your appointment.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BookingPage;
