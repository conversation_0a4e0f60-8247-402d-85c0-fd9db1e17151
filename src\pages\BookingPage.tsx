import React, { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import WhatsAppButton from "@/components/home/<USER>";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  CalendarIcon,
  Clock,
  Info,
  Car,
  Wrench,
  Zap,
  Settings,
  Battery,
  Gauge,
  AlertTriangle,
  Shield,
  Phone,
  MapPin,
  Star,
  Users,
  Award,
  Timer,
  CreditCard,
  FileText,
  Truck
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format, isWeekend, addDays, isBefore, isToday } from "date-fns";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";

const availableServices = [
  {
    id: "engine_diagnostics",
    name: "Engine Diagnostics",
    price: "KES 2,000",
    duration: "45-60 mins",
    description: "Comprehensive engine performance analysis and fault detection",
    icon: Car,
    category: "Engine",
    popular: true
  },
  {
    id: "full_system_scan",
    name: "Full System Scan",
    price: "KES 3,000",
    duration: "60-90 mins",
    description: "Complete vehicle diagnostic scan covering all systems",
    icon: Settings,
    category: "Comprehensive",
    popular: true
  },
  {
    id: "battery_charging_test",
    name: "Battery & Charging Test",
    price: "KES 1,500",
    duration: "30-45 mins",
    description: "Battery health check and charging system analysis",
    icon: Battery,
    category: "Electrical",
    popular: false
  },
  {
    id: "pre_purchase_inspection",
    name: "Pre-Purchase Inspection",
    price: "KES 3,500",
    duration: "90-120 mins",
    description: "Thorough inspection for vehicle buyers",
    icon: FileText,
    category: "Inspection",
    popular: true
  },
  {
    id: "cooling_system_check",
    name: "Cooling System Check",
    price: "KES 2,000",
    duration: "45-60 mins",
    description: "Radiator, thermostat, and cooling system diagnostics",
    icon: Gauge,
    category: "Engine",
    popular: false
  },
  {
    id: "transmission_diagnostics",
    name: "Transmission Diagnostics",
    price: "KES 2,500",
    duration: "60-75 mins",
    description: "Automatic and manual transmission system analysis",
    icon: Settings,
    category: "Transmission",
    popular: false
  },
  {
    id: "brake_system_check",
    name: "Brake System Check",
    price: "KES 1,800",
    duration: "45-60 mins",
    description: "Brake pads, discs, and hydraulic system inspection",
    icon: AlertTriangle,
    category: "Safety",
    popular: false
  },
  {
    id: "fleet_diagnostics",
    name: "Fleet Diagnostics",
    price: "From KES 5,000",
    duration: "2-4 hours",
    description: "Multiple vehicle diagnostic services for businesses",
    icon: Truck,
    category: "Fleet",
    popular: false
  },
  {
    id: "service_light_reset",
    name: "Service Light Reset",
    price: "KES 800",
    duration: "15-30 mins",
    description: "Reset service indicators and maintenance lights",
    icon: Wrench,
    category: "Maintenance",
    popular: false
  },
  {
    id: "obd_fault_code_interpretation",
    name: "OBD Fault Code Reading",
    price: "KES 1,000",
    duration: "30-45 mins",
    description: "Read and interpret diagnostic trouble codes",
    icon: Zap,
    category: "Diagnostic",
    popular: false
  },
];

const emergencyServices = [
  {
    id: "emergency_engine_failure",
    name: "Emergency Engine Failure",
    price: "KES 2,500",
    duration: "30-60 mins",
    description: "Urgent engine diagnostic for breakdown situations",
    icon: AlertTriangle,
    responseTime: "&lt; 30 mins"
  },
  {
    id: "emergency_electrical",
    name: "Emergency Electrical Issues",
    price: "KES 2,000",
    duration: "30-45 mins",
    description: "Urgent electrical system diagnostics",
    icon: Zap,
    responseTime: "&lt; 30 mins"
  },
  {
    id: "emergency_battery",
    name: "Emergency Battery Service",
    price: "KES 1,500",
    duration: "20-30 mins",
    description: "Emergency battery testing and jump start",
    icon: Battery,
    responseTime: "&lt; 25 mins"
  }
];

const STAGE_TITLES = [
  "Select Your Service",
  "Vehicle Information",
  "Contact & Location",
  "Schedule & Confirm"
];

const trustIndicators = [
  { icon: Users, label: "500+ Happy Customers", value: "500+" },
  { icon: Star, label: "5-Star Average Rating", value: "5.0★" },
  { icon: Award, label: "Certified Technicians", value: "100%" },
  { icon: Timer, label: "On-Time Guarantee", value: "98%" }
];

const Stage1Form = ({ onNext, currentData }: { onNext: (data: any) => void; currentData: any }) => {
  const [selectedServices, setSelectedServices] = useState<string[]>(currentData?.services || []);
  const [isEmergency, setIsEmergency] = useState(currentData?.isEmergency || false);
  const [serviceFilter, setServiceFilter] = useState<string>("all");

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices((prevSelected) =>
      prevSelected.includes(serviceId)
        ? prevSelected.filter((id) => id !== serviceId)
        : [...prevSelected, serviceId]
    );
  };

  const handleEmergencyToggle = (emergency: boolean) => {
    setIsEmergency(emergency);
    if (emergency) {
      setSelectedServices([]);
    }
  };

  const handleNextClick = () => {
    if (selectedServices.length === 0) {
      toast.error("Please select at least one service.");
      return;
    }
    onNext({ services: selectedServices, isEmergency });
  };

  const servicesToShow = isEmergency ? emergencyServices : availableServices;
  const filteredServices = serviceFilter === "all"
    ? servicesToShow
    : servicesToShow.filter(service => service.category === serviceFilter);

  const categories = [...new Set(availableServices.map(s => s.category))];
  const totalPrice = selectedServices.reduce((total, serviceId) => {
    const service = servicesToShow.find(s => s.id === serviceId);
    if (service && !service.price.includes("From")) {
      return total + parseInt(service.price.replace(/[^\d]/g, ''));
    }
    return total;
  }, 0);

  return (
    <div className="space-y-8">
      {/* Emergency Toggle */}
      <div className="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-red-800">Emergency Service Needed?</h3>
              <p className="text-sm text-red-600">Get priority response within 30 minutes</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant={!isEmergency ? "default" : "outline"}
              size="sm"
              onClick={() => handleEmergencyToggle(false)}
              className={!isEmergency ? "bg-automotive-blue hover:bg-automotive-blue/90" : ""}
            >
              Regular Service
            </Button>
            <Button
              variant={isEmergency ? "default" : "outline"}
              size="sm"
              onClick={() => handleEmergencyToggle(true)}
              className={isEmergency ? "bg-red-600 hover:bg-red-700" : "border-red-300 text-red-600 hover:bg-red-50"}
            >
              <AlertTriangle className="w-4 h-4 mr-2" />
              Emergency
            </Button>
          </div>
        </div>
      </div>

      {/* Service Selection */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-automotive-dark">
              {isEmergency ? "Emergency Services" : "Select Your Services"}
            </h3>
            <p className="text-muted-foreground">
              {isEmergency
                ? "Choose from our emergency diagnostic services"
                : "Choose one or more diagnostic services for your vehicle"
              }
            </p>
          </div>
          {!isEmergency && (
            <Select value={serviceFilter} onValueChange={setServiceFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Services</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        <div className="grid md:grid-cols-2 gap-4 max-h-96 overflow-y-auto pr-2">
          {filteredServices.map((service) => {
            const isSelected = selectedServices.includes(service.id);
            const IconComponent = service.icon;

            return (
              <motion.div
                key={service.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:shadow-lg",
                    isSelected
                      ? "border-automotive-orange bg-orange-50 shadow-md"
                      : "border-border hover:border-automotive-orange/50"
                  )}
                  onClick={() => handleServiceToggle(service.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={cn(
                        "w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0",
                        isSelected ? "bg-automotive-orange text-white" : "bg-automotive-blue/10 text-automotive-blue"
                      )}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-automotive-dark text-sm leading-tight">
                            {service.name}
                          </h4>
                          {service.popular && !isEmergency && (
                            <Badge variant="secondary" className="text-xs bg-automotive-orange/10 text-automotive-orange">
                              Popular
                            </Badge>
                          )}
                          {isEmergency && (
                            <Badge variant="secondary" className="text-xs bg-red-100 text-red-600">
                              {service.responseTime}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                          {service.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-bold text-automotive-orange">
                            {service.price}
                          </span>
                          <span className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {service.duration}
                          </span>
                        </div>
                      </div>
                      <div className={cn(
                        "w-5 h-5 rounded border-2 flex items-center justify-center",
                        isSelected
                          ? "bg-automotive-orange border-automotive-orange"
                          : "border-gray-300"
                      )}>
                        {isSelected && <CheckCircle className="w-3 h-3 text-white" />}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Selected Services Summary */}
        {selectedServices.length > 0 && (
          <div className="mt-6 p-4 bg-automotive-blue/5 border border-automotive-blue/20 rounded-lg">
            <h4 className="font-semibold text-automotive-dark mb-2">Selected Services:</h4>
            <div className="space-y-1 mb-3">
              {selectedServices.map(id => {
                const service = servicesToShow.find(s => s.id === id);
                return service ? (
                  <div key={id} className="flex justify-between text-sm">
                    <span>{service.name}</span>
                    <span className="font-medium">{service.price}</span>
                  </div>
                ) : null;
              })}
            </div>
            {totalPrice > 0 && (
              <div className="border-t border-automotive-blue/20 pt-2">
                <div className="flex justify-between font-bold text-automotive-dark">
                  <span>Estimated Total:</span>
                  <span>KES {totalPrice.toLocaleString()}</span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleNextClick}
          className="bg-automotive-orange hover:bg-automotive-orange/90 text-white px-8 py-3"
          disabled={selectedServices.length === 0}
        >
          Continue to Vehicle Details
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const Stage2Form = ({ onNext, onPrev, currentData }: { onNext: (data: any) => void; onPrev: () => void; currentData: any }) => {
  const [vehicleMake, setVehicleMake] = useState(currentData?.vehicleMake || "");
  const [vehicleModel, setVehicleModel] = useState(currentData?.vehicleModel || "");
  const [vehicleYear, setVehicleYear] = useState(currentData?.vehicleYear || "");
  const [vehicleVIN, setVehicleVIN] = useState(currentData?.vehicleVIN || "");
  const [mileage, setMileage] = useState(currentData?.mileage || "");
  const [fuelType, setFuelType] = useState(currentData?.fuelType || "");
  const [transmission, setTransmission] = useState(currentData?.transmission || "");
  const [issues, setIssues] = useState(currentData?.issues || "");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const popularMakes = ["Toyota", "Nissan", "Honda", "Mazda", "Subaru", "Mitsubishi", "Volkswagen", "BMW", "Mercedes-Benz", "Audi"];
  const fuelTypes = ["Petrol", "Diesel", "Hybrid", "Electric"];
  const transmissionTypes = ["Manual", "Automatic", "CVT"];

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!vehicleMake.trim()) newErrors.vehicleMake = "Vehicle make is required.";
    if (!vehicleModel.trim()) newErrors.vehicleModel = "Vehicle model is required.";
    if (!vehicleYear) {
      newErrors.vehicleYear = "Vehicle year is required.";
    } else {
      const yearNum = parseInt(vehicleYear, 10);
      if (isNaN(yearNum) || yearNum < 1980 || yearNum > new Date().getFullYear() + 1) {
        newErrors.vehicleYear = `Year must be between 1980 and ${new Date().getFullYear() + 1}.`;
      }
    }
    if (vehicleVIN.trim() && vehicleVIN.trim().length !== 17) {
      newErrors.vehicleVIN = "VIN must be exactly 17 characters if provided.";
    }
    if (mileage && (isNaN(Number(mileage)) || Number(mileage) < 0)) {
      newErrors.mileage = "Please enter a valid mileage.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextClick = () => {
    if (validate()) {
      onNext({
        vehicleMake,
        vehicleModel,
        vehicleYear,
        vehicleVIN,
        mileage,
        fuelType,
        transmission,
        issues
      });
    }
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="w-16 h-16 bg-automotive-blue/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Car className="w-8 h-8 text-automotive-blue" />
        </div>
        <h3 className="text-xl font-bold text-automotive-dark mb-2">Vehicle Information</h3>
        <p className="text-muted-foreground">Help us prepare the right tools and expertise for your vehicle</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="vehicleMake" className="text-sm font-medium text-automotive-dark">Vehicle Make *</Label>
            <Select value={vehicleMake} onValueChange={setVehicleMake}>
              <SelectTrigger className={`mt-1 ${errors.vehicleMake ? 'border-destructive' : ''}`}>
                <SelectValue placeholder="Select or type make" />
              </SelectTrigger>
              <SelectContent>
                {popularMakes.map(make => (
                  <SelectItem key={make} value={make}>{make}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            {!popularMakes.includes(vehicleMake) && (
              <Input
                value={vehicleMake}
                onChange={(e) => setVehicleMake(e.target.value)}
                placeholder="Type vehicle make"
                className="mt-2"
              />
            )}
            {errors.vehicleMake && <p className="text-xs text-destructive mt-1">{errors.vehicleMake}</p>}
          </div>

          <div>
            <Label htmlFor="vehicleModel" className="text-sm font-medium text-automotive-dark">Vehicle Model *</Label>
            <Input
              id="vehicleModel"
              value={vehicleModel}
              onChange={(e) => setVehicleModel(e.target.value)}
              placeholder="e.g., Corolla, Camry, X-Trail"
              className={`mt-1 ${errors.vehicleModel ? 'border-destructive' : ''}`}
            />
            {errors.vehicleModel && <p className="text-xs text-destructive mt-1">{errors.vehicleModel}</p>}
          </div>

          <div>
            <Label htmlFor="vehicleYear" className="text-sm font-medium text-automotive-dark">Vehicle Year *</Label>
            <Input
              id="vehicleYear"
              type="number"
              value={vehicleYear}
              onChange={(e) => setVehicleYear(e.target.value)}
              placeholder="e.g., 2015"
              min="1980"
              max={new Date().getFullYear() + 1}
              className={`mt-1 ${errors.vehicleYear ? 'border-destructive' : ''}`}
            />
            {errors.vehicleYear && <p className="text-xs text-destructive mt-1">{errors.vehicleYear}</p>}
          </div>

          <div>
            <Label htmlFor="mileage" className="text-sm font-medium text-automotive-dark">Mileage (km)</Label>
            <Input
              id="mileage"
              type="number"
              value={mileage}
              onChange={(e) => setMileage(e.target.value)}
              placeholder="e.g., 150000"
              className={`mt-1 ${errors.mileage ? 'border-destructive' : ''}`}
            />
            {errors.mileage && <p className="text-xs text-destructive mt-1">{errors.mileage}</p>}
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="fuelType" className="text-sm font-medium text-automotive-dark">Fuel Type</Label>
            <Select value={fuelType} onValueChange={setFuelType}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select fuel type" />
              </SelectTrigger>
              <SelectContent>
                {fuelTypes.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="transmission" className="text-sm font-medium text-automotive-dark">Transmission</Label>
            <Select value={transmission} onValueChange={setTransmission}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select transmission" />
              </SelectTrigger>
              <SelectContent>
                {transmissionTypes.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="vehicleVIN" className="text-sm font-medium text-automotive-dark">
              Vehicle VIN
              <span className="text-xs text-muted-foreground ml-1">(Optional, 17 characters)</span>
            </Label>
            <Input
              id="vehicleVIN"
              value={vehicleVIN}
              onChange={(e) => setVehicleVIN(e.target.value.toUpperCase())}
              placeholder="17-character Vehicle Identification Number"
              maxLength={17}
              className={`mt-1 ${errors.vehicleVIN ? 'border-destructive' : ''}`}
            />
            {errors.vehicleVIN && <p className="text-xs text-destructive mt-1">{errors.vehicleVIN}</p>}
            {vehicleVIN && vehicleVIN.length < 17 && (
              <p className="text-xs text-muted-foreground mt-1">
                {17 - vehicleVIN.length} characters remaining
              </p>
            )}
          </div>
        </div>
      </div>

      <div>
        <Label htmlFor="issues" className="text-sm font-medium text-automotive-dark">
          Current Issues or Symptoms
          <span className="text-xs text-muted-foreground ml-1">(Optional)</span>
        </Label>
        <Textarea
          id="issues"
          value={issues}
          onChange={(e) => setIssues(e.target.value)}
          placeholder="Describe any problems you're experiencing with your vehicle (e.g., engine noise, warning lights, performance issues)"
          className="mt-1 min-h-[100px]"
          maxLength={500}
        />
        <p className="text-xs text-muted-foreground mt-1">
          {issues.length}/500 characters
        </p>
      </div>

      {/* Vehicle Summary */}
      {vehicleMake && vehicleModel && vehicleYear && (
        <div className="bg-automotive-blue/5 border border-automotive-blue/20 rounded-lg p-4">
          <h4 className="font-semibold text-automotive-dark mb-2">Vehicle Summary</h4>
          <p className="text-automotive-blue font-medium">
            {vehicleYear} {vehicleMake} {vehicleModel}
            {fuelType && ` • ${fuelType}`}
            {transmission && ` • ${transmission}`}
            {mileage && ` • ${Number(mileage).toLocaleString()} km`}
          </p>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-3 justify-between">
        <Button onClick={onPrev} variant="outline" className="w-full sm:w-auto">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Services
        </Button>
        <Button
          onClick={handleNextClick}
          className="w-full sm:w-auto bg-automotive-orange hover:bg-automotive-orange/90 text-white"
        >
          Continue to Contact Details
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const Stage3Form = ({ onNext, onPrev, currentData }: { onNext: (data: any) => void; onPrev: () => void; currentData: any }) => {
  const [fullName, setFullName] = useState(currentData?.fullName || "");
  const [phone, setPhone] = useState(currentData?.phone || "");
  const [email, setEmail] = useState(currentData?.email || "");
  const [address, setAddress] = useState(currentData?.address || "");
  const [city, setCity] = useState(currentData?.city || "Nairobi");
  const [landmark, setLandmark] = useState(currentData?.landmark || "");
  const [preferredContact, setPreferredContact] = useState(currentData?.preferredContact || "phone");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const kenyanCities = ["Nairobi", "Mombasa", "Kisumu", "Nakuru", "Eldoret", "Thika", "Machakos", "Meru", "Nyeri", "Kericho"];

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!fullName.trim()) newErrors.fullName = "Full name is required.";
    if (!phone.trim()) {
      newErrors.phone = "Phone number is required.";
    } else if (!/^(07|01)\d{8}$/.test(phone) && !/^\+254\d{9}$/.test(phone)) {
      newErrors.phone = "Please enter a valid Kenyan phone number (e.g., 07XXXXXXXX or +254XXXXXXXXX).";
    }
    if (!email.trim()) {
      newErrors.email = "Email address is required.";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Please enter a valid email address.";
    }
    if (!address.trim()) newErrors.address = "Address/Estate is required.";
    if (!city.trim()) newErrors.city = "City/Town is required.";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextClick = () => {
    if (validate()) {
      onNext({
        fullName,
        phone,
        email,
        address,
        city,
        landmark,
        preferredContact
      });
    }
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="w-16 h-16 bg-automotive-orange/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Phone className="w-8 h-8 text-automotive-orange" />
        </div>
        <h3 className="text-xl font-bold text-automotive-dark mb-2">Contact & Location Details</h3>
        <p className="text-muted-foreground">We'll use this information to reach you and locate your vehicle</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="fullName" className="text-sm font-medium text-automotive-dark">Full Name *</Label>
            <Input
              id="fullName"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              placeholder="e.g., John Doe"
              className={`mt-1 ${errors.fullName ? 'border-destructive' : ''}`}
            />
            {errors.fullName && <p className="text-xs text-destructive mt-1">{errors.fullName}</p>}
          </div>

          <div>
            <Label htmlFor="phone" className="text-sm font-medium text-automotive-dark">Phone Number *</Label>
            <Input
              id="phone"
              type="tel"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              placeholder="e.g., 0712345678 or +254712345678"
              className={`mt-1 ${errors.phone ? 'border-destructive' : ''}`}
            />
            {errors.phone && <p className="text-xs text-destructive mt-1">{errors.phone}</p>}
            <p className="text-xs text-muted-foreground mt-1">
              We'll use this to contact you and send updates
            </p>
          </div>

          <div>
            <Label htmlFor="email" className="text-sm font-medium text-automotive-dark">Email Address *</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="e.g., <EMAIL>"
              className={`mt-1 ${errors.email ? 'border-destructive' : ''}`}
            />
            {errors.email && <p className="text-xs text-destructive mt-1">{errors.email}</p>}
            <p className="text-xs text-muted-foreground mt-1">
              For booking confirmation and service reports
            </p>
          </div>

          <div>
            <Label htmlFor="preferredContact" className="text-sm font-medium text-automotive-dark">Preferred Contact Method</Label>
            <Select value={preferredContact} onValueChange={setPreferredContact}>
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="phone">Phone Call</SelectItem>
                <SelectItem value="sms">SMS/Text</SelectItem>
                <SelectItem value="whatsapp">WhatsApp</SelectItem>
                <SelectItem value="email">Email</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="address" className="text-sm font-medium text-automotive-dark">Address / Estate *</Label>
            <Input
              id="address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="e.g., Uhuru Estate, House No. 123"
              className={`mt-1 ${errors.address ? 'border-destructive' : ''}`}
            />
            {errors.address && <p className="text-xs text-destructive mt-1">{errors.address}</p>}
          </div>

          <div>
            <Label htmlFor="city" className="text-sm font-medium text-automotive-dark">City / Town *</Label>
            <Select value={city} onValueChange={setCity}>
              <SelectTrigger className={`mt-1 ${errors.city ? 'border-destructive' : ''}`}>
                <SelectValue placeholder="Select your city" />
              </SelectTrigger>
              <SelectContent>
                {kenyanCities.map(cityName => (
                  <SelectItem key={cityName} value={cityName}>{cityName}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            {!kenyanCities.includes(city) && (
              <Input
                value={city}
                onChange={(e) => setCity(e.target.value)}
                placeholder="Type your city/town"
                className="mt-2"
              />
            )}
            {errors.city && <p className="text-xs text-destructive mt-1">{errors.city}</p>}
          </div>

          <div>
            <Label htmlFor="landmark" className="text-sm font-medium text-automotive-dark">
              Nearby Landmark
              <span className="text-xs text-muted-foreground ml-1">(Optional)</span>
            </Label>
            <Input
              id="landmark"
              value={landmark}
              onChange={(e) => setLandmark(e.target.value)}
              placeholder="e.g., Near Sarit Centre, Opposite Shell Station"
              className="mt-1"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Help our technician find you easily
            </p>
          </div>
        </div>
      </div>

      {/* Service Area Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <MapPin className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-blue-800 mb-1">Service Area Coverage</h4>
            <p className="text-sm text-blue-700 mb-2">
              We currently provide mobile diagnostic services in Nairobi and surrounding areas.
              If you're outside our coverage area, we'll contact you to discuss alternatives.
            </p>
            <div className="flex items-center gap-4 text-xs text-blue-600">
              <span className="flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Nairobi CBD
              </span>
              <span className="flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Westlands
              </span>
              <span className="flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Karen
              </span>
              <span className="flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Kiambu
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Summary */}
      {fullName && phone && email && (
        <div className="bg-automotive-orange/5 border border-automotive-orange/20 rounded-lg p-4">
          <h4 className="font-semibold text-automotive-dark mb-2">Contact Summary</h4>
          <div className="space-y-1 text-sm">
            <p><span className="font-medium">Name:</span> {fullName}</p>
            <p><span className="font-medium">Phone:</span> {phone}</p>
            <p><span className="font-medium">Email:</span> {email}</p>
            <p><span className="font-medium">Location:</span> {address}{city && `, ${city}`}</p>
            {landmark && <p><span className="font-medium">Landmark:</span> {landmark}</p>}
            <p><span className="font-medium">Preferred Contact:</span> {preferredContact}</p>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-3 justify-between">
        <Button onClick={onPrev} variant="outline" className="w-full sm:w-auto">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Vehicle Info
        </Button>
        <Button
          onClick={handleNextClick}
          className="w-full sm:w-auto bg-automotive-orange hover:bg-automotive-orange/90 text-white"
        >
          Continue to Schedule
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const Stage4Form = ({ onPrev, onSubmit, bookingData, currentData }: { onPrev: () => void; onSubmit: (data: any) => void; bookingData: any; currentData: any }) => {
  const [preferredDate, setPreferredDate] = useState<Date | undefined>(currentData?.preferredDate);
  const [preferredTime, setPreferredTime] = useState(currentData?.preferredTime || "");
  const [specialInstructions, setSpecialInstructions] = useState(currentData?.specialInstructions || "");
  const [agreedToTerms, setAgreedToTerms] = useState(currentData?.agreedToTerms || false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Available time slots
  const timeSlots = [
    { id: "morning-early", label: "8:00 AM - 10:00 AM", value: "8:00 AM - 10:00 AM" },
    { id: "morning-late", label: "10:00 AM - 12:00 PM", value: "10:00 AM - 12:00 PM" },
    { id: "afternoon-early", label: "1:00 PM - 3:00 PM", value: "1:00 PM - 3:00 PM" },
    { id: "afternoon-late", label: "3:00 PM - 5:00 PM", value: "3:00 PM - 5:00 PM" },
  ];

  // Function to check if a date is available
  const isDateAvailable = (date: Date) => {
    // Example: Weekends have limited availability
    if (isWeekend(date)) {
      // Only morning slots on weekends in this example
      return "limited";
    }
    return "available";
  };

  // Function to check if a time slot is available for the selected date
  const isTimeSlotAvailable = (slot: string) => {
    if (!preferredDate) return true; // Show all slots as available when no date is selected

    // Example: Morning slots only on weekends
    if (isWeekend(preferredDate)) {
      return slot.includes("AM");
    }
    return true;
  };

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    setPreferredDate(date);
    
    // If the currently selected time is not available for the new date, clear it
    if (date && preferredTime && !isTimeSlotAvailable(preferredTime)) {
      setPreferredTime("");
    }
    
    // Clear date-related error when a date is selected
    if (date && errors.preferredDate) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.preferredDate;
        return newErrors;
      });
    }
  };

  // Handle time slot selection
  const handleTimeSelect = (value: string) => {
    setPreferredTime(value);
    
    // Clear time-related error when a time is selected
    if (value && errors.preferredTime) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.preferredTime;
        return newErrors;
      });
    }
  };

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!preferredDate) newErrors.preferredDate = "Preferred date is required.";
    if (!preferredTime) newErrors.preferredTime = "Preferred time slot is required.";
    if (!agreedToTerms) newErrors.agreedToTerms = "Please agree to the terms and conditions.";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmitClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (validate()) {
      setIsSubmitting(true);
      onSubmit({
        preferredDate,
        preferredTime,
        specialInstructions,
        agreedToTerms
      });
    }
  };

  const getServiceName = (id: string) => availableServices.find(s => s.id === id)?.name || id;

  const servicesToShow = bookingData.isEmergency ? emergencyServices : availableServices;
  const totalPrice = bookingData.services?.reduce((total: number, serviceId: string) => {
    const service = servicesToShow.find(s => s.id === serviceId);
    if (service && !service.price.includes("From")) {
      return total + parseInt(service.price.replace(/[^\d]/g, ''));
    }
    return total;
  }, 0) || 0;

  const totalDuration = bookingData.services?.reduce((total: number, serviceId: string) => {
    const service = servicesToShow.find(s => s.id === serviceId);
    if (service) {
      const duration = service.duration.split('-')[1] || service.duration.split('-')[0];
      return total + parseInt(duration.replace(/[^\d]/g, ''));
    }
    return total;
  }, 0) || 0;

  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-xl font-bold text-automotive-dark mb-2">Review & Schedule Your Service</h3>
        <p className="text-muted-foreground">Confirm your details and choose your preferred appointment time</p>
      </div>

      {/* Booking Summary */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Services Summary */}
        <Card className="border-automotive-blue/20">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-automotive-dark flex items-center gap-2">
              <Wrench className="w-5 h-5 text-automotive-blue" />
              Selected Services
              {bookingData.isEmergency && (
                <Badge variant="destructive" className="text-xs">Emergency</Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {bookingData.services && bookingData.services.length > 0 ? (
              bookingData.services.map((serviceId: string) => {
                const service = servicesToShow.find(s => s.id === serviceId);
                return service ? (
                  <div key={serviceId} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-0">
                    <div>
                      <p className="font-medium text-sm">{service.name}</p>
                      <p className="text-xs text-muted-foreground">{service.duration}</p>
                    </div>
                    <span className="font-semibold text-automotive-orange">{service.price}</span>
                  </div>
                ) : null;
              })
            ) : (
              <p className="text-muted-foreground text-sm">No services selected.</p>
            )}
            {totalPrice > 0 && (
              <div className="border-t border-automotive-blue/20 pt-3 mt-3">
                <div className="flex justify-between font-bold text-automotive-dark">
                  <span>Estimated Total:</span>
                  <span>KES {totalPrice.toLocaleString()}</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Estimated duration: {totalDuration} minutes
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Vehicle & Contact Summary */}
        <Card className="border-automotive-orange/20">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-automotive-dark flex items-center gap-2">
              <Car className="w-5 h-5 text-automotive-orange" />
              Vehicle & Contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h5 className="font-semibold text-automotive-dark text-sm mb-2">Vehicle:</h5>
              <p className="text-sm">
                {bookingData.vehicleYear} {bookingData.vehicleMake} {bookingData.vehicleModel}
              </p>
              {bookingData.fuelType && (
                <p className="text-xs text-muted-foreground">
                  {bookingData.fuelType} • {bookingData.transmission}
                  {bookingData.mileage && ` • ${Number(bookingData.mileage).toLocaleString()} km`}
                </p>
              )}
            </div>
            <div>
              <h5 className="font-semibold text-automotive-dark text-sm mb-2">Contact:</h5>
              <p className="text-sm">{bookingData.fullName}</p>
              <p className="text-xs text-muted-foreground">{bookingData.phone}</p>
              <p className="text-xs text-muted-foreground">{bookingData.email}</p>
            </div>
            <div>
              <h5 className="font-semibold text-automotive-dark text-sm mb-2">Location:</h5>
              <p className="text-sm">{bookingData.address}</p>
              <p className="text-xs text-muted-foreground">
                {bookingData.city}
                {bookingData.landmark && ` • Near ${bookingData.landmark}`}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scheduling Section */}
      <Card className="border-green-200">
        <CardHeader>
          <CardTitle className="text-lg text-automotive-dark flex items-center gap-2">
            <CalendarIcon className="w-5 h-5 text-green-600" />
            Schedule Your Appointment
          </CardTitle>
          <CardDescription>
            Choose your preferred date and time for the mobile diagnostic service
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Date Selection */}
            <div>
              <div className="flex items-center mb-3">
                <Label className="font-medium text-automotive-dark">Select Date</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>Select your preferred service date. Weekends have limited availability (morning slots only).</p>
                      <ul className="mt-2 text-xs">
                        <li className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-automotive-orange mr-2"></div>
                          <span>Available</span>
                        </li>
                        <li className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-amber-500 mr-2"></div>
                          <span>Limited availability</span>
                        </li>
                      </ul>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <Calendar
                  mode="single"
                  selected={preferredDate}
                  onSelect={handleDateSelect}
                  disabled={(date) => isBefore(date, addDays(new Date(), -1))}
                  initialFocus
                  className="rounded-md"
                  classNames={{
                    day_selected: "bg-automotive-orange text-white hover:bg-automotive-orange hover:text-white focus:bg-automotive-orange focus:text-white",
                    day_today: "bg-muted text-foreground",
                  }}
                />
              </div>
              {errors.preferredDate && <p className="text-xs text-destructive mt-1">{errors.preferredDate}</p>}

              {preferredDate && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center text-sm">
                    <CalendarIcon className="h-4 w-4 mr-2 text-green-600" />
                    <span>
                      Selected: <span className="font-medium">{format(preferredDate, "EEEE, MMMM d, yyyy")}</span>
                      {isWeekend(preferredDate) && (
                        <span className="ml-2 text-amber-600 text-xs">(Weekend - limited availability)</span>
                      )}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Time Selection */}
            <div>
              <div className="flex items-center mb-3">
                <Label className="font-medium text-automotive-dark">Select Time Slot</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Choose your preferred time slot. On weekends, only morning slots are available.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <RadioGroup
                value={preferredTime}
                onValueChange={handleTimeSelect}
                className="grid grid-cols-1 gap-3"
              >
                {timeSlots.map((slot) => {
                  const isAvailable = isTimeSlotAvailable(slot.value);
                  return (
                    <div key={slot.id} className={cn(
                      "relative",
                      !isAvailable && preferredDate && "opacity-50"
                    )}>
                      <RadioGroupItem
                        value={slot.value}
                        id={slot.id}
                        className="peer sr-only"
                        disabled={!isAvailable && !!preferredDate}
                      />
                      <Label
                        htmlFor={slot.id}
                        className={cn(
                          "flex items-center justify-between rounded-lg border-2 border-gray-200 bg-white p-4",
                          "hover:bg-gray-50 hover:border-automotive-orange/50",
                          "peer-data-[state=checked]:border-automotive-orange peer-data-[state=checked]:bg-orange-50",
                          "transition-all cursor-pointer",
                          !isAvailable && preferredDate && "cursor-not-allowed"
                        )}
                      >
                        <div className="flex items-center">
                          <Clock className="h-5 w-5 mr-3 text-automotive-blue" />
                          <span className="font-medium">{slot.label}</span>
                        </div>
                        {preferredTime === slot.value && (
                          <CheckCircle className="h-5 w-5 text-automotive-orange" />
                        )}
                      </Label>
                    </div>
                  );
                })}
              </RadioGroup>
              {!preferredDate && (
                <p className="text-xs text-muted-foreground mt-2">
                  💡 Select a date first to see availability restrictions for time slots
                </p>
              )}
              {preferredDate && timeSlots.every(slot => !isTimeSlotAvailable(slot.value)) && (
                <p className="text-xs text-amber-600 mt-2">No time slots available for the selected date. Please choose another date.</p>
              )}
              {errors.preferredTime && <p className="text-xs text-destructive mt-1">{errors.preferredTime}</p>}
            </div>
          </div>

          {/* Special Instructions */}
          <div>
            <Label htmlFor="specialInstructions" className="font-medium text-automotive-dark">
              Special Instructions
              <span className="text-xs text-muted-foreground ml-1">(Optional)</span>
            </Label>
            <Textarea
              id="specialInstructions"
              value={specialInstructions}
              onChange={(e) => setSpecialInstructions(e.target.value)}
              placeholder="Any special instructions for our technician (e.g., gate code, parking instructions, specific concerns)"
              className="mt-2 min-h-[80px]"
              maxLength={300}
            />
            <p className="text-xs text-muted-foreground mt-1">
              {specialInstructions.length}/300 characters
            </p>
          </div>

          {/* Terms and Conditions */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-start gap-3">
              <Checkbox
                id="terms"
                checked={agreedToTerms}
                onCheckedChange={setAgreedToTerms}
                className="mt-1"
              />
              <div className="flex-1">
                <Label htmlFor="terms" className="text-sm font-medium text-automotive-dark cursor-pointer">
                  I agree to the Terms and Conditions and Privacy Policy
                </Label>
                <p className="text-xs text-muted-foreground mt-1">
                  By booking this service, you agree to our terms of service and privacy policy.
                  You can cancel or reschedule up to 2 hours before your appointment.
                </p>
                {errors.agreedToTerms && <p className="text-xs text-destructive mt-1">{errors.agreedToTerms}</p>}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Final Summary and Actions */}
      {preferredDate && preferredTime && (
        <div className="bg-gradient-to-r from-automotive-blue to-automotive-dark text-white rounded-lg p-6">
          <h4 className="font-bold text-lg mb-3">Appointment Summary</h4>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="opacity-90">Date & Time:</p>
              <p className="font-semibold">
                {format(preferredDate, "EEEE, MMMM d, yyyy")} at {preferredTime}
              </p>
            </div>
            <div>
              <p className="opacity-90">Estimated Duration:</p>
              <p className="font-semibold">{totalDuration} minutes</p>
            </div>
            <div>
              <p className="opacity-90">Service Type:</p>
              <p className="font-semibold">
                {bookingData.isEmergency ? "Emergency Service" : "Regular Service"}
              </p>
            </div>
            <div>
              <p className="opacity-90">Total Cost:</p>
              <p className="font-semibold">
                {totalPrice > 0 ? `KES ${totalPrice.toLocaleString()}` : "Quote on arrival"}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        <Button
          onClick={onPrev}
          variant="outline"
          className="w-full sm:w-auto"
          disabled={isSubmitting}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Contact Details
        </Button>
        <Button
          onClick={handleSubmitClick}
          className="w-full sm:flex-1 bg-automotive-orange hover:bg-automotive-orange/90 text-white text-lg py-6"
          disabled={isSubmitting || !preferredDate || !preferredTime || !agreedToTerms}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin mr-2 h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
              Confirming Your Booking...
            </>
          ) : (
            <>
              <CheckCircle className="mr-2 h-5 w-5" />
              Confirm Booking - {bookingData.isEmergency ? "Emergency Service" : "Schedule Service"}
            </>
          )}
        </Button>
      </div>
    </div>
  );
};


const BookingPage = () => {
  const [currentStage, setCurrentStage] = useState(0);
  const [bookingData, setBookingData] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  
  // Ref to store scroll position
  const scrollPositionRef = useRef(0);
  
  // Ref to the booking card element
  const bookingCardRef = useRef<HTMLDivElement>(null);

  // Function to save current scroll position
  const saveScrollPosition = useCallback(() => {
    scrollPositionRef.current = window.scrollY;
  }, []);

  // Function to restore saved scroll position
  const restoreScrollPosition = useCallback(() => {
    window.scrollTo({
      top: scrollPositionRef.current,
      behavior: 'auto' // Use auto to prevent visible scrolling
    });
  }, []);

  const handleNext = (stageData: any) => {
    // Only handle scrolling for normal navigation, not during submission
    if (!isSubmitting) {
      saveScrollPosition();
    }
    
    setBookingData((prev) => ({ ...prev, ...stageData }));
    
    if (currentStage < STAGE_TITLES.length - 1) {
      setCurrentStage((prev) => prev + 1);
      
      // Only scroll for normal navigation between stages
      if (!isSubmitting && bookingCardRef.current) {
        bookingCardRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      }
    }
  };

  const handlePrev = () => {
    if (currentStage > 0) {
      setCurrentStage((prev) => prev - 1);
    }
  };

  const handleSubmit = (finalData: any) => {
    // Save scroll position before any state changes
    saveScrollPosition();
    
    // Set submitting state to prevent unwanted scrolling
    setIsSubmitting(true);
    
    // Merge the final data with existing booking data
    const completeBookingData = { ...bookingData, ...finalData };
    
    // Simulate API call with a timeout
    setTimeout(() => {
      console.log("Final Booking Data:", completeBookingData);
      
      // Show toast notification
      toast.success("Booking Confirmed!", {
        description: "Your service has been scheduled successfully. You'll receive a confirmation email shortly.",
        duration: 5000,
        action: {
          label: "View Details",
          onClick: () => console.log("View booking details")
        },
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        className: "bg-white border-green-500",
        position: "top-center"
      });
      
      // Update states in a single batch to minimize re-renders
      setIsCompleted(true);
      setCurrentStage(STAGE_TITLES.length);
      
      // Use requestAnimationFrame to ensure scroll position is restored after DOM updates
      requestAnimationFrame(() => {
        restoreScrollPosition();
        setIsSubmitting(false);
      });
    }, 1500); // Simulate network delay
  };

  // Use effect to handle scroll position restoration after state changes
  useEffect(() => {
    if (isSubmitting) {
      // Prevent scrolling during submission by restoring position after any DOM updates
      const handleScroll = () => {
        if (isSubmitting) {
          restoreScrollPosition();
        }
      };
      
      window.addEventListener('scroll', handleScroll, { passive: true });
      
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isSubmitting, restoreScrollPosition]);

  const progressValue = ((currentStage + 1) / STAGE_TITLES.length) * 100;

  const stageVariants = {
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 },
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-automotive-blue to-automotive-dark text-white py-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-6">
                Mobile Diagnostic Booking
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold mb-6">
                Book Your
                <span className="text-automotive-orange"> Mobile Service</span>
              </h1>
              <p className="text-xl text-primary-foreground mb-8 leading-relaxed">
                Professional automotive diagnostics at your location. Quick, convenient, and reliable.
              </p>

              {/* Trust Indicators */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8">
                {trustIndicators.map((indicator, index) => (
                  <div key={index} className="text-center">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <indicator.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-lg font-bold">{indicator.value}</div>
                    <div className="text-sm text-white/80">{indicator.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Booking Form Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
            <Card ref={bookingCardRef} className="shadow-2xl border-0 rounded-2xl overflow-hidden bg-white">
              <CardHeader className="bg-white border-b border-gray-100 p-8">
                <div className="text-center">
                  <CardTitle className="text-3xl font-bold text-automotive-dark mb-2">
                    {isCompleted ? "Booking Confirmed!" : "Complete Your Booking"}
                  </CardTitle>
                  <CardDescription className="text-lg text-muted-foreground">
                    {isCompleted
                      ? "Your mobile diagnostic service has been scheduled successfully"
                      : "Follow these simple steps to schedule your mobile diagnostic service"
                    }
                  </CardDescription>
                </div>

                {!isCompleted && (
                  <div className="mt-8">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-automotive-dark">
                        Step {currentStage + 1}: {STAGE_TITLES[currentStage]}
                      </h3>
                      <span className="text-sm text-muted-foreground">
                        {currentStage + 1} of {STAGE_TITLES.length}
                      </span>
                    </div>
                    <Progress
                      value={progressValue}
                      className="w-full h-3 bg-gray-200"
                    />
                  </div>
                )}
              </CardHeader>

              <CardContent className="p-8">
                {!isCompleted ? (
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentStage}
                      variants={stageVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      transition={{ duration: 0.3 }}
                    >
                      {currentStage === 0 && <Stage1Form onNext={handleNext} currentData={bookingData} />}
                      {currentStage === 1 && <Stage2Form onNext={handleNext} onPrev={handlePrev} currentData={bookingData} />}
                      {currentStage === 2 && <Stage3Form onNext={handleNext} onPrev={handlePrev} currentData={bookingData} />}
                      {currentStage === 3 && <Stage4Form onPrev={handlePrev} onSubmit={handleSubmit} bookingData={bookingData} currentData={bookingData} />}
                    </motion.div>
                  </AnimatePresence>
                ) : (
                  <motion.div
                    className="text-center py-12"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <CheckCircle className="w-12 h-12 text-green-600" />
                    </div>
                    <h3 className="text-3xl font-bold text-automotive-dark mb-4">Booking Confirmed!</h3>
                    <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                      Thank you for choosing DiagOnWheels! We've received your booking and will contact you shortly
                      to confirm your appointment details. You'll also receive a confirmation email.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button
                        onClick={() => window.location.reload()}
                        className="bg-automotive-blue hover:bg-automotive-blue/90"
                      >
                        Book Another Service
                      </Button>
                      <Button variant="outline" asChild>
                        <a href="tel:+254727795520">
                          <Phone className="w-4 h-4 mr-2" />
                          Call Us: 0727 795 520
                        </a>
                      </Button>
                    </div>
                  </motion.div>
                )}
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Additional Information Section */}
        {!isCompleted && (
          <section className="py-16 bg-white">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="max-w-4xl mx-auto">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-automotive-dark mb-4">
                    Why Choose DiagOnWheels?
                  </h2>
                  <p className="text-lg text-muted-foreground">
                    Professional mobile automotive diagnostics with guaranteed quality and convenience
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-automotive-blue/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Shield className="w-8 h-8 text-automotive-blue" />
                    </div>
                    <h3 className="text-xl font-bold text-automotive-dark mb-2">Certified Technicians</h3>
                    <p className="text-muted-foreground">
                      All our technicians are certified professionals with years of experience
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-automotive-orange/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MapPin className="w-8 h-8 text-automotive-orange" />
                    </div>
                    <h3 className="text-xl font-bold text-automotive-dark mb-2">Mobile Convenience</h3>
                    <p className="text-muted-foreground">
                      We come to your location - home, office, or roadside
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CreditCard className="w-8 h-8 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold text-automotive-dark mb-2">Transparent Pricing</h3>
                    <p className="text-muted-foreground">
                      No hidden fees - you know the cost upfront before we start
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}
      </main>

      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default BookingPage;
