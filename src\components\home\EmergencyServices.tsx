
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { 
  AlertTriangle, 
  Phone, 
  Clock, 
  MapPin, 
  Car, 
  Wrench, 
  ShieldCheck,
  ArrowRight
} from "lucide-react";
import { Link } from "react-router-dom";

const EmergencyServices = () => {
  const emergencyFeatures = [
    {
      icon: Clock,
      title: "24/7 Availability",
      description: "Our emergency team is available around the clock, including weekends and holidays."
    },
    {
      icon: MapPin,
      title: "Rapid Response",
      description: "Average arrival time of 30 minutes or less within our service area."
    },
    {
      icon: Car,
      title: "Roadside Assistance",
      description: "Immediate help for breakdowns, no matter where you are stranded."
    },
    {
      icon: Wrench,
      title: "On-the-Spot Repairs",
      description: "Many common issues can be fixed on location to get you back on the road."
    },
    {
      icon: <PERSON><PERSON>he<PERSON>,
      title: "Safety First",
      description: "Our technicians ensure you and your vehicle remain safe throughout the process."
    },
    {
      icon: Phone,
      title: "Live Updates",
      description: "Receive real-time updates on technician arrival and service progress."
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-4">
            Emergency Support
          </Badge>
          <h2 className="text-4xl font-bold text-automotive-dark mb-6">
            24/7 Emergency
            <span className="text-automotive-blue"> Diagnostic Services</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Vehicle troubles don't follow a schedule. That's why our emergency team is available
            around the clock to provide immediate assistance when you need it most.
          </p>
        </div>

        {/* Emergency Features */}
        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {emergencyFeatures.map((feature, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full border border-border hover:border-automotive-orange/50 transition-all duration-300 hover:shadow-md">
                <CardContent className="p-6">
                  <div className="flex items-start">
                    <div className="bg-automotive-orange/10 p-3 rounded-lg mr-4 flex-shrink-0">
                      <feature.icon className="w-6 h-6 text-automotive-orange" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-automotive-dark mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-muted-foreground text-sm">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Emergency Contact Banner */}
        <motion.div 
          className="bg-gradient-to-r from-automotive-orange to-automotive-orange/90 rounded-2xl p-8 mb-16 text-center text-white shadow-xl overflow-hidden relative"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {/* Animated Pulse */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div 
              className="w-40 h-40 rounded-full bg-white/10"
              animate={{ 
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.1, 0.3]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>

          <div className="relative z-10">
            <div className="flex items-center justify-center mb-4">
              <Phone className="w-12 h-12 text-white" />
            </div>
            <h3 className="text-3xl font-bold mb-2">Emergency Hotline</h3>
            <div className="text-4xl font-bold mb-4">0727 795 520</div>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              Call now for immediate roadside assistance. Our dispatch team will locate the nearest
              certified technician and provide real-time updates.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-automotive-orange hover:bg-automotive-orange/90 text-white" 
                asChild
              >
                <a href="tel:+254727795520">
                  <Phone className="w-5 h-5 mr-2" />
                  Call Emergency Line
                </a>
              </Button>
              <Link to="/emergency-services">
                <Button size="lg" variant="outline" className="border-white/30 bg-transparent hover:bg-white/10 text-white">
                  Learn More
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Service Guarantee */}
        <div className="bg-automotive-gray/10 rounded-xl p-8 text-center">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="flex items-center">
              <AlertTriangle className="w-10 h-10 text-automotive-orange mr-4 flex-shrink-0" />
              <div className="text-left">
                <h3 className="text-xl font-bold text-automotive-dark mb-1">Our Emergency Service Guarantee</h3>
                <p className="text-muted-foreground">
                  We guarantee a technician will arrive within 60 minutes of your call or your diagnostic fee is waived.
                </p>
              </div>
            </div>
            <Button className="bg-automotive-blue hover:bg-automotive-blue/90 text-white whitespace-nowrap">
              View Service Area
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EmergencyServices;



