
"use client";

import React from "react";
import { Sidebar<PERSON>rovider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "./AdminSidebar";
import { Separator } from "@/components/ui/separator";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

interface AdminLayoutProps {
  children: React.ReactNode;
  breadcrumbs?: Array<{ label: string; href?: string }>;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children, breadcrumbs = [] }) => {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-gray-50">
        <AppSidebar />
        <SidebarInset className="flex-1">
          <header className="flex h-20 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-16 bg-white border-b border-gray-200 shadow-sm">
            <div className="flex items-center gap-4 px-6">
              <SidebarTrigger className="-ml-1 p-2 hover:bg-gray-100 rounded-lg transition-colors" />
              <Separator orientation="vertical" className="mr-2 h-6 bg-gray-300" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink 
                      href="/admin"
                      className="text-automotive-blue hover:text-automotive-blue/80 font-semibold"
                    >
                      Admin Console
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  {breadcrumbs.map((breadcrumb, index) => (
                    <React.Fragment key={index}>
                      <BreadcrumbSeparator className="hidden md:block text-gray-400" />
                      <BreadcrumbItem>
                        {breadcrumb.href ? (
                          <BreadcrumbLink 
                            href={breadcrumb.href}
                            className="text-gray-600 hover:text-automotive-blue font-medium"
                          >
                            {breadcrumb.label}
                          </BreadcrumbLink>
                        ) : (
                          <BreadcrumbPage className="text-automotive-dark font-semibold">
                            {breadcrumb.label}
                          </BreadcrumbPage>
                        )}
                      </BreadcrumbItem>
                    </React.Fragment>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-6 p-8 pt-6">
            {children}
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default AdminLayout;
