import React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { 
  Car, 
  ShieldCheck, 
  Users, 
  Award, 
  Clock, 
  Wrench, 
  ThumbsUp, 
  HeartHandshake 
} from "lucide-react";

const WhyChooseUs = () => {
  const benefits = [
    {
      icon: Car,
      title: "Convenience at Your Doorstep",
      description: "No more waiting in line or arranging for a tow. Our mobile mechanics come to you, saving you time and hassle.",
      color: "text-automotive-blue",
      bgColor: "bg-automotive-blue/10"
    },
    {
      icon: ShieldCheck,
      title: "Certified & Experienced Technicians",
      description: "Our team consists of highly trained and certified professionals dedicated to providing top-notch service.",
      color: "text-automotive-orange",
      bgColor: "bg-automotive-orange/10"
    },
    {
      icon: Users,
      title: "Transparent Pricing",
      description: "We believe in honesty. You'll receive a clear, upfront quote before any work begins, with no hidden fees.",
      color: "text-automotive-blue",
      bgColor: "bg-automotive-blue/10"
    },
    {
      icon: Award,
      title: "Quality Guaranteed",
      description: "We stand by our work with a comprehensive warranty on all parts and services, ensuring your peace of mind.",
      color: "text-automotive-orange",
      bgColor: "bg-automotive-orange/10"
    },
    {
      icon: Clock,
      title: "Fast Response Times",
      description: "Our technicians arrive promptly within the scheduled window, respecting your time and schedule.",
      color: "text-automotive-blue",
      bgColor: "bg-automotive-blue/10"
    },
    {
      icon: Wrench,
      title: "Advanced Diagnostic Tools",
      description: "We use the latest technology and equipment to accurately diagnose and resolve your vehicle issues.",
      color: "text-automotive-orange",
      bgColor: "bg-automotive-orange/10"
    },
    {
      icon: ThumbsUp,
      title: "Customer Satisfaction Focus",
      description: "Your satisfaction is our priority. We're not happy until you're completely satisfied with our service.",
      color: "text-automotive-blue",
      bgColor: "bg-automotive-blue/10"
    },
    {
      icon: HeartHandshake,
      title: "Relationship-Based Service",
      description: "We build lasting relationships with our customers through trust, reliability, and exceptional service.",
      color: "text-automotive-orange",
      bgColor: "bg-automotive-orange/10"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section className="py-20 bg-automotive-gray/5">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-4">
            Our Advantages
          </Badge>
          <h2 className="text-4xl font-bold text-automotive-dark mb-6">
            Why Choose <span className="text-automotive-blue">DiagOnWheels</span>?
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We're committed to providing exceptional mobile automotive diagnostic services
            with a focus on convenience, quality, and customer satisfaction.
          </p>
        </div>

        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {benefits.map((benefit, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full border border-border hover:border-automotive-blue/50 transition-all duration-300 hover:shadow-md group">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className={`p-4 rounded-full ${benefit.bgColor} mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <benefit.icon className={`w-8 h-8 ${benefit.color}`} />
                    </div>
                    <h3 className="text-xl font-semibold text-automotive-dark mb-3 group-hover:text-automotive-blue transition-colors">
                      {benefit.title}
                    </h3>
                    <p className="text-muted-foreground">
                      {benefit.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
